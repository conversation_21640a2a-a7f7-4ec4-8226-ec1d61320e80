# Mathematical Solving System Documentation

## Overview

The solving system provides robust mathematical algorithms for polynomial equations, complex number operations, and geometric coefficient manipulation. It forms the mathematical foundation for advanced geometric constructions and calculations.

## Architecture

### Solving Components

```
solving/
├── complex.ts          # Complex number arithmetic
├── poly.solving.ts     # Polynomial equation solving
└── coefficients.ts     # Ellipse coefficient manipulation
```

## Complex Number System

### Complex Class

The `Complex` class provides comprehensive complex number operations:

```typescript
export class Complex {
    static EPSILON: number = 1e-8;
    private _re: number;
    private _im: number;
    
    constructor(re: number = 0, im: number = 0) {
        this._re = re;
        this._im = im;
    }
    
    // Getters
    get real(): number { return this._re; }
    get imag(): number { return this._im; }
    
    // Basic operations
    add(n: number | Complex): Complex;
    sub(n: number | Complex): Complex;
    mult(n: number | Complex): Complex;
    div(n: number | Complex): Complex;
}
```

### Complex Number Creation

```typescript
// Static factory methods
const z1 = Complex.fromRI(3, 4);           // 3 + 4i
const z2 = Complex.fromPolar(5, Math.PI/4); // 5∠45°
const z3 = Complex.fromZ(z1);              // Copy constructor

// Direct construction
const z4 = new Complex(2, -3);             // 2 - 3i
```

### Arithmetic Operations

```typescript
const z1 = new Complex(3, 4);
const z2 = new Complex(1, 2);

// Basic arithmetic
const sum = z1.add(z2);        // (3+4i) + (1+2i) = 4+6i
const diff = z1.sub(z2);       // (3+4i) - (1+2i) = 2+2i
const product = z1.mult(z2);   // (3+4i) * (1+2i) = -5+10i
const quotient = z1.div(z2);   // (3+4i) / (1+2i) = 2.2-0.4i

// With real numbers
const scaled = z1.mult(2);     // 2 * (3+4i) = 6+8i
const shifted = z1.add(5);     // (3+4i) + 5 = 8+4i
```

### Advanced Operations

```typescript
const z = new Complex(3, 4);

// Magnitude and phase
const magnitude = z.abs();           // |z| = 5
const phase = z.arg();              // arg(z) = arctan(4/3)

// Powers and roots
const squared = z.squared();         // z²
const cubed = z.cubed();            // z³
const power = z.pow(2.5);           // z^2.5
const roots = z.roots(3);           // Cube roots of z

// Trigonometric functions
const exp = z.exp();                // e^z
const log = z.log();                // ln(z)
const sqrt = z.sqrt();              // √z
```

### Utility Methods

```typescript
const z1 = new Complex(3, 4);
const z2 = new Complex(3, 4.0000001);

// Comparison with tolerance
const isEqual = z1.equals(z2, 1e-6);    // true
const isReal = z1.isReal();              // false
const isZero = Complex.ZERO.isZero();    // true

// Conversion and display
const conjugate = z1.conjugate();        // 3 - 4i
const negated = z1.negate();            // -3 - 4i
const string = z1.toString();           // "3 + 4i"
```

## Polynomial Solving

### Base Polynomial Class

```typescript
export class PolyBase {
    protected cf: number[];
    protected all_roots: Complex[];
    
    static getPoly(coefs: number[]): PolyBase {
        // Automatically determines polynomial degree and returns appropriate class
        switch (coefs.length) {
            case 5: return new Quartic(coefs);
            case 4: return new Cubic(coefs);
            case 3: return new Quadratic(coefs);
            case 2: return new Linear(coefs);
            default: throw new Error('Invalid coefficients');
        }
    }
    
    roots(): Complex[] {
        return [...this.all_roots];
    }
    
    eval(x: number | Complex): Complex {
        // Evaluate polynomial at given point
    }
}
```

### Linear Equations

Solves equations of the form: `a₁x + a₀ = 0`

```typescript
export class Linear extends PolyBase {
    constructor(coefs: number[]) {
        super();
        // coefs = [a1, a0] where a1 ≠ 0
        if (coefs.length !== 2 || coefs[0] === 0) {
            throw new Error('Invalid coefficients for linear equation');
        }
        this.cf = coefs.reverse().map(c => c / coefs[0]);
        this.all_roots = [new Complex(-this.cf[0], 0)];
    }
}

// Usage
const linear = new Linear([2, -6]);  // 2x - 6 = 0
const roots = linear.roots();        // [3 + 0i]
```

### Quadratic Equations

Solves equations of the form: `a₂x² + a₁x + a₀ = 0`

```typescript
export class Quadratic extends PolyBase {
    constructor(coefs: number[]) {
        super();
        // coefs = [a2, a1, a0] where a2 ≠ 0
        if (coefs.length !== 3 || coefs[0] === 0) {
            throw new Error('Invalid coefficients for quadratic equation');
        }
        this.cf = coefs.reverse().map(c => c / coefs[0]);
        this.all_roots = this.__solve();
    }
    
    private __solve(): Complex[] {
        const a1 = this.cf[1], a0 = this.cf[0];
        const discriminant = a1 * a1 - 4 * a0;
        const sqrtDisc = Math.sqrt(Math.abs(discriminant));
        
        if (discriminant >= 0) {
            // Real roots
            return [
                new Complex((-a1 + sqrtDisc) / 2, 0),
                new Complex((-a1 - sqrtDisc) / 2, 0)
            ];
        } else {
            // Complex roots
            return [
                new Complex(-a1 / 2, sqrtDisc / 2),
                new Complex(-a1 / 2, -sqrtDisc / 2)
            ];
        }
    }
}

// Usage
const quadratic = new Quadratic([1, -5, 6]);  // x² - 5x + 6 = 0
const roots = quadratic.roots();               // [2 + 0i, 3 + 0i]
```

### Cubic Equations

Solves equations of the form: `a₃x³ + a₂x² + a₁x + a₀ = 0`

```typescript
class Cubic extends PolyBase {
    private d: number[];
    private SHIFT: Complex;
    
    constructor(coefs: number[]) {
        super();
        // coefs = [a3, a2, a1, a0] where a3 ≠ 0
        if (coefs.length !== 4 || coefs[0] === 0) {
            throw new Error('Invalid coefficients for cubic equation');
        }
        
        const a3 = coefs[0];
        this.cf = coefs.reverse().map(c => c / a3);
        this.all_roots = this.__solve();
    }
    
    private __solve(): Complex[] {
        this._calcDepressedCubic();
        const roots: Complex[] = [];
        const S = this.SHIFT;
        
        if (this.d[1] === 0) {
            // Simplified case: x³ + d₀ = 0
            roots.push(new Complex(Math.cbrt(-this.d[0]), 0).sub(S));
        } else {
            // General case using Cardano's formula
            const R = new Complex(-this.d[0] / 2, 0);
            const Q = new Complex(this.d[1] / 3, 0);
            const W3 = R.sub(R.squared().add(Q.cubed()).sqrt());
            const Wroots = W3.roots(3);
            
            for (const root of Wroots) {
                roots.push(root.sub(Q.mult(root.pow(-1))).sub(S));
            }
        }
        
        return roots;
    }
}
```

### Quartic Equations

Solves equations of the form: `a₄x⁴ + a₃x³ + a₂x² + a₁x + a₀ = 0`

```typescript
export class Quartic extends PolyBase {
    private d: number[];
    private SHIFT: Complex;
    
    constructor(coefs: number[]) {
        super();
        // coefs = [a4, a3, a2, a1, a0] where a4 ≠ 0
        if (coefs.length !== 5 || coefs[0] === 0) {
            throw new Error('Invalid coefficients for quartic equation');
        }
        
        const a4 = coefs[0];
        this.cf = coefs.reverse().map(c => c / a4);
        this.all_roots = this.__solve();
    }
    
    private __solve(): Complex[] {
        this.__calcDepressedQuartic();
        
        if (this.d[1] === 0) {
            // Biquadratic case: x⁴ + d₂x² + d₀ = 0
            return this.__solveBiquadratic();
        } else {
            // General case using Ferrari's method
            return this.__solveGeneral();
        }
    }
}
```

## Coefficient System

### Ellipse Coefficients

The `Coefficients` class handles ellipse equation manipulation:

```typescript
export class Coefficients {
    a: number; b: number; c: number;
    d: number; e: number; f: number;
    
    constructor(a: number, b: number, c: number, d: number, e: number, f: number) {
        // Represents: ax² + bxy + cy² + dx + ey + f = 0
        this.a = a; this.b = b; this.c = c;
        this.d = d; this.e = e; this.f = f;
    }
    
    // Arithmetic operations
    add(other: Coefficients): Coefficients;
    subtract(other: Coefficients): Coefficients;
    mult(scalar: number): Coefficients;
    divide(scalar: number): Coefficients;
    
    // Coefficient manipulation
    getCoef(name: string): number;
    copy(): Coefficients;
}
```

### Coefficient Operations

```typescript
const ellipse1 = new Coefficients(1, 0, 1, 0, 0, -1);  // x² + y² - 1 = 0 (unit circle)
const ellipse2 = new Coefficients(4, 0, 1, 0, 0, -4);  // 4x² + y² - 4 = 0

// Arithmetic operations
const sum = ellipse1.add(ellipse2);
const scaled = ellipse1.mult(2);
const normalized = ellipse1.divide(ellipse1.getCoef('f'));

// Term elimination
const eliminated = Coefficients.elliminateTerm(ellipse1, ellipse2, 'a');
```

## Usage in Geometry

### Intersection Calculations

```typescript
// Find intersection of two circles
function findCircleIntersections(
    circle1: { cx: number, cy: number, r: number },
    circle2: { cx: number, cy: number, r: number }
): Point[] {
    // Set up system of equations
    const coefs = [
        1, 0, 1, -2 * circle1.cx, -2 * circle1.cy, 
        circle1.cx * circle1.cx + circle1.cy * circle1.cy - circle1.r * circle1.r
    ];
    
    // Solve quadratic system
    const poly = PolyBase.getPoly(coefs);
    const roots = poly.roots();
    
    // Filter real solutions
    return Complex.filterRealRoots(roots).map(root => ({
        x: root.real,
        y: calculateY(root.real, circle1, circle2)
    }));
}
```

### Curve Fitting

```typescript
// Fit ellipse to points
function fitEllipseToPoints(points: Point[]): Coefficients {
    // Set up least squares system
    const matrix = points.map(p => [
        p.x * p.x, p.x * p.y, p.y * p.y, p.x, p.y, 1
    ]);
    
    // Solve for coefficients
    const solution = solveLeastSquares(matrix);
    
    return new Coefficients(
        solution[0], solution[1], solution[2],
        solution[3], solution[4], solution[5]
    );
}
```

### Root Finding

```typescript
// Find roots of polynomial defined by geometric constraints
function solveGeometricConstraint(constraints: GeometricConstraint[]): Point[] {
    // Convert constraints to polynomial coefficients
    const coefficients = constraintsToPolynomial(constraints);
    
    // Solve polynomial
    const poly = PolyBase.getPoly(coefficients);
    const roots = poly.roots();
    
    // Filter and convert to geometric points
    const realRoots = Complex.filterRealRoots(roots);
    return realRoots.map(root => ({ x: root.real, y: 0 }));
}
```

## Performance Considerations

### Numerical Stability

```typescript
// Use appropriate epsilon for comparisons
const EPSILON = 1e-10;

function isNearZero(value: number): boolean {
    return Math.abs(value) < EPSILON;
}

function areEqual(a: number, b: number): boolean {
    return Math.abs(a - b) < EPSILON;
}
```

### Optimization Strategies

1. **Coefficient Normalization**: Normalize leading coefficients to 1
2. **Root Polishing**: Refine roots using Newton's method
3. **Caching**: Cache expensive calculations
4. **Early Termination**: Stop when sufficient precision is reached

### Error Handling

```typescript
try {
    const poly = new Quadratic([0, 1, 2]); // Invalid: leading coefficient is 0
} catch (error) {
    console.error('Invalid polynomial coefficients:', error.message);
}

// Check for numerical issues
const roots = poly.roots();
const validRoots = roots.filter(root => 
    Number.isFinite(root.real) && Number.isFinite(root.imag)
);
```

## Best Practices

1. **Input Validation**: Always validate polynomial coefficients
2. **Numerical Precision**: Use appropriate epsilon values
3. **Root Verification**: Verify roots by substitution
4. **Error Handling**: Handle degenerate cases gracefully
5. **Performance**: Cache expensive calculations
6. **Testing**: Test with known analytical solutions

## Integration Examples

### Tool Integration

```typescript
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    private findIntersections(line: RenderLine, circle: RenderCircle): Point[] {
        // Set up quadratic equation for line-circle intersection
        const a = line.a, b = line.b, c = line.c;
        const cx = circle.centerX, cy = circle.centerY, r = circle.radius;
        
        // Substitute line equation into circle equation
        const coeffs = this.buildIntersectionCoefficients(a, b, c, cx, cy, r);
        
        // Solve quadratic
        const quadratic = new Quadratic(coeffs);
        const roots = quadratic.roots();
        
        // Convert to points
        return Complex.filterRealRoots(roots).map(root => 
            this.rootToPoint(root, line)
        );
    }
}
```

### Constraint Solving

```typescript
export class ConstraintSolver {
    solveDistanceConstraint(
        point1: Point, 
        point2: Point, 
        distance: number
    ): Point[] {
        // (x - p1.x)² + (y - p1.y)² = d²
        // (x - p2.x)² + (y - p2.y)² = d²
        
        const coeffs = this.buildDistanceConstraintCoefficients(
            point1, point2, distance
        );
        
        const poly = PolyBase.getPoly(coeffs);
        return this.convertRootsToPoints(poly.roots());
    }
}
