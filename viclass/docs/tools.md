# Tools System Documentation

## Overview

The tools system provides a comprehensive set of geometry creation and manipulation tools. Each tool follows a consistent pattern and integrates with the selector system for element interaction.

## Tool Architecture

### Base Tool Class

All geometry tools extend the `GeometryTool` base class:

```typescript
export abstract class GeometryTool<T extends ToolState> extends Tool<T, GeometryToolBar> {
    protected focusDocCtrl: GeoDocCtrl;
    protected editor: GeometryEditor;
    
    // Event handlers
    abstract onPointerDown(event: GeoPointerEvent): void;
    onPointerMove?(event: GeoPointerEvent): void;
    onPointerUp?(event: GeoPointerEvent): void;
    onKeyDown?(event: GeoKeyboardEvent): void;
}
```

### Tool State Management

Each tool maintains its state through a `ToolState` object:

```typescript
export class CommonToolState implements ToolState {}

export class TriangleToolState extends CommonToolState {
    drawMode: number = 0; // 0: by 3 points, 1: by base and height
}

export class QuadToolState extends CommonToolState {
    drawMode: number = 0; // Different modes for different quadrilaterals
}
```

## Point Tools

### CreatePointTool

Creates points at specified locations:

```typescript
export class CreatePointTool extends GeometryTool<CommonToolState> {
    private selector = vertex({ preview: true, autoAccept: true });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (selected) {
            this.createPoint(selected);
        }
    }
}
```

### MiddlePointTool

Creates midpoints between two points:

```typescript
export class MiddlePointTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertex({ preview: true }), { 
        minCount: 2, 
        maxCount: 2 
    });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createMiddlePoint(selected);
        }
    }
}
```

### IntersectionPointTool

Creates intersection points between geometric objects:

```typescript
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    private selector = repeat(stroke({
        selectableStrokeTypes: ['RenderLine', 'RenderCircle', 'RenderEllipse']
    }), { minCount: 2, maxCount: 2 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createIntersectionPoints(selected);
        }
    }
}
```

## Line Tools

### CreateLineTool

Creates infinite lines through two points:

```typescript
export class CreateLineTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createLine(selected);
        }
    }
}
```

### CreateLineSegmentTool

Creates line segments between two points:

```typescript
export class CreateLineSegmentTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createLineSegment(selected);
        }
    }
}
```

### CreateParallelLineTool

Creates lines parallel to existing lines:

```typescript
export class CreateParallelLineTool extends GeometryTool<CommonToolState> {
    private selector = then([
        stroke({ selectableStrokeTypes: ['RenderLine'] }),
        vertex({ preview: true })
    ]);
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createParallelLine(selected);
        }
    }
}
```

## Shape Tools

### CreateCircleTool

Creates circles with center and radius:

```typescript
export class CreateCircleTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createCircle(selected);
        }
    }
}
```

### CreateTriangleTool

Creates triangles with various modes:

```typescript
export class CreateTriangleTool extends GeometryTool<TriangleToolState> {
    private selector = repeat(vertexS(), { minCount: 3, maxCount: 3 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createTriangle(selected);
        }
    }
}
```

### CreateSquareTool

Creates squares with special construction logic:

```typescript
export class CreateSquareTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createSquare(selected);
        }
    }
    
    private createSquare(vertices: SelectedVertex[]): void {
        const [v1, v2] = vertices;
        // Calculate square vertices using direction algorithms
        const direction = nthDirectionByLine(
            [v2.x - v1.x, v2.y - v1.y], 
            [v1.x, v1.y], 
            [v2.x, v2.y]
        );
        // Create square construction...
    }
}
```

## Transformation Tools

### MoveElementTool

Moves geometric elements:

```typescript
export class MoveElementTool extends GeometryTool<CommonToolState> {
    private selector = or([
        vertex({ realEl: true }),
        stroke({ selectableStrokeTypes: ['RenderLine', 'RenderCircle'] })
    ]);
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (selected) {
            this.startMove(selected, event);
        }
    }
}
```

## Tool Construction Patterns

### Construction Requests

Tools create geometric objects through construction requests:

```typescript
interface GeoElConstructionRequest {
    ctId: string;
    elType: GeoObjectType;
    cgName: string;
    paramSpecs?: ParamSpecs[];
}

// Example: Point construction
const pointConstruction: GeoElConstructionRequest = {
    ctId: 'Point',
    elType: 'Point',
    cgName: 'PointByCoordinates',
    paramSpecs: [
        { paramKind: 'value', value: x },
        { paramKind: 'value', value: y }
    ]
};
```

### Remote Construction

Tools use remote construction for server-side validation:

```typescript
async function remoteConstruct(
    editor: GeometryEditor,
    constructions: ConstructionRequest[],
    docCtrl: GeoDocCtrl
): Promise<ApplyConstructionResponse> {
    return await editor.geoGateway.construct(
        docCtrl.state.globalId,
        constructions
    );
}
```

## Tool Integration

### Selector Integration

Tools integrate with the selector system:

```typescript
export class ExampleTool extends GeometryTool<CommonToolState> {
    // Define selectors for different interaction modes
    private pointSelector = vertex({ preview: true });
    private lineSelector = stroke({ selectableStrokeTypes: ['RenderLine'] });
    private combinedSelector = or([this.pointSelector, this.lineSelector]);
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.combinedSelector.trySelect(event, this.focusDocCtrl);
        if (selected) {
            this.handleSelection(selected);
        }
    }
}
```

### Preview Management

Tools manage preview elements during construction:

```typescript
private previewQueue = new PreviewQueue();

onPointerMove(event: GeoPointerEvent): void {
    // Clear previous previews
    this.previewQueue.clear();
    
    // Create new preview
    const previewElement = this.createPreviewElement(event);
    this.previewQueue.add(previewElement);
    
    // Flush to renderer
    await this.previewQueue.flush(this.focusDocCtrl);
}
```

## Tool Lifecycle

1. **Initialization**: Tool is created and added to toolbar
2. **Activation**: Tool becomes active when selected
3. **Interaction**: Tool handles pointer and keyboard events
4. **Construction**: Tool creates geometric objects
5. **Deactivation**: Tool is deactivated when another tool is selected

## Best Practices

1. **Use Selectors**: Always use the selector DSL for element selection
2. **State Management**: Maintain tool state properly
3. **Preview Elements**: Provide visual feedback during construction
4. **Error Handling**: Handle invalid constructions gracefully
5. **Performance**: Optimize for smooth interaction
6. **Consistency**: Follow established patterns for similar tools
