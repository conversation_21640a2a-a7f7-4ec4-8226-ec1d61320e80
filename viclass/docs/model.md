# Model System Documentation

## Overview

The model system defines the data structures, object models, and type definitions used throughout the geometry editor. It provides a comprehensive type system for geometric objects, rendering properties, and API communication.

## Architecture

### Model Categories

The model system is organized into several categories:

```
model/
├── geo.models.ts           # Core geometry types and configurations
├── geo.object.models.ts    # Object collections and management
├── render.elements.model.ts # Render elements and properties
├── gateway.models.ts       # API communication models
├── geo.tool.model.ts      # Tool state models
├── name.pattern.model.ts  # Naming patterns and validation
└── util.preview.ts        # Preview utilities
```

## Core Geometry Models

### Geometry Object Types

```typescript
export type GeoObjectType =
    | 'Point' | 'Line' | 'LineSegment' | 'Ray' | 'Vector'
    | 'Circle' | 'Ellipse' | 'Semicircle' | 'CircularSector'
    | 'Triangle' | 'RightTriangle' | 'IsoscelesTriangle' | 'EquilateralTriangle'
    | 'Polygon' | 'RegularPolygon' | 'Quadrilateral'
    | 'Rectangle' | 'Square' | 'Parallelogram' | 'Rhombus' | 'Trapezoid'
    | 'Angle';

export const GeoObjectTypeValue = [
    'Angle', 'Point', 'Vector', 'Line', 'LineSegment', 'Ray',
    'Circle', 'Ellipse', 'Semicircle', 'CircularSector',
    'Triangle', 'RightTriangle', 'IsoscelesTriangle',
    'IsoscelesRightTriangle', 'EquilateralTriangle',
    'Polygon', 'RegularPolygon', 'Quadrilateral',
    'Rectangle', 'Square', 'Parallelogram', 'Rhombus', 'Trapezoid'
];
```

### Tool Types

```typescript
export type GeometryToolType =
    | 'CreatePointTool' | 'CreateLineTool' | 'CreateCircleTool'
    | 'CreateTriangleTool' | 'CreateSquareTool' | 'CreateAngleTool'
    | 'IntersectionPointTool' | 'MiddlePointTool' | 'PointOnObjectTool'
    | 'CreateParallelLineTool' | 'CreatePerpendicularLineTool'
    | 'MoveElementTool' | 'UpdatePropTool' | 'RenameElementTool'
    | 'GeoPanTool' | 'GeoZoomTool' | 'InputCommandTool';
```

### Document Models

```typescript
export interface GeoDoc extends VDoc {
    kind: GeoKind;
    docRenderProp: DocRenderProp;
    docDefaultElRenderProps: DefaultGeoRenderProp;
    layers: GeoLayer[];
}

export interface GeoLayer extends VDocLayer {
    boundary: BoundaryRectangle;
}

export type GeoDocInitData = {
    numDim: number;
    kind: GeoKind;
    boundary?: BoundaryRectangle;
    docRenderProp: DocRenderProp;
};
```

## Render Element Models

### Base Render Element

```typescript
export abstract class GeoRenderElement {
    abstract readonly type: GeoRelType;
    abstract readonly elType: GeoObjectType;
    abstract renderProp?: GeoRenderProp;
    
    vertexRelIdxes?: number[];
    lineRelIdxes?: number[];
    arcRelIdx?: number;
    
    unselectable?: boolean;
    relIndex: number;
    name: string = '';
    usable: boolean = true;
    deleted?: boolean;
    valid: boolean = true;
    pInfo?: GeoPreviewInfo = undefined;
}
```

### Vertex Elements

```typescript
export class RenderVertex extends GeoRenderElement {
    readonly type: GeoRelType = 'Vertex';
    readonly elType: GeoObjectType = 'Point';
    
    x: number;
    y: number;
    renderProp?: VertexGeoRenderProp;
    
    constructor(x: number, y: number, relIndex: number) {
        super();
        this.x = x;
        this.y = y;
        this.relIndex = relIndex;
    }
}
```

### Line Elements

```typescript
export class RenderLine extends GeoRenderElement {
    readonly type: GeoRelType = 'Line';
    readonly elType: GeoObjectType = 'LineVi';
    
    a: number; // Line equation: ax + by + c = 0
    b: number;
    c: number;
    renderProp?: LineGeoRenderProp;
    
    // Helper methods
    vector(renderer?: GeoRenderer): number[] {
        return [-this.b, this.a]; // Direction vector
    }
    
    distanceToPoint(x: number, y: number): number {
        return Math.abs(this.a * x + this.b * y + this.c) / 
               Math.sqrt(this.a * this.a + this.b * this.b);
    }
}

export class RenderLineSegment extends RenderLine {
    readonly type: GeoRelType = 'LineSegment';
    readonly elType: GeoObjectType = 'LineSegment';
    
    length?: number;
    
    startPoint(renderer?: GeoRenderer): Point {
        // Calculate start point from vertex indices
    }
    
    endPoint(renderer?: GeoRenderer): Point {
        // Calculate end point from vertex indices
    }
}
```

### Circle Elements

```typescript
export class RenderCircle extends GeoRenderElement {
    readonly type: GeoRelType = 'Circle';
    readonly elType: GeoObjectType = 'Circle';
    
    centerX: number;
    centerY: number;
    radius: number;
    renderProp?: CircleGeoRenderProp;
    
    length?: number; // Circumference
    
    containsPoint(x: number, y: number): boolean {
        const dx = x - this.centerX;
        const dy = y - this.centerY;
        return dx * dx + dy * dy <= this.radius * this.radius;
    }
}

export class RenderCircleShape extends RenderCircle {
    readonly type: GeoRelType = 'CircleShape';
    area?: number;
}
```

### Polygon Elements

```typescript
export class RenderPolygon extends GeoRenderElement {
    readonly type: GeoRelType = 'Polygon';
    readonly elType: GeoObjectType = 'Polygon';
    
    renderProp?: PolygonGeoRenderProp;
    area?: number;
    
    vertices(renderer?: GeoRenderer): Point[] {
        // Get vertices from vertex indices
    }
    
    containsPoint(x: number, y: number, renderer?: GeoRenderer): boolean {
        // Point-in-polygon test
    }
}
```

## Render Properties

### Base Render Properties

```typescript
export interface GeoRenderProp {
    lineWeight?: number;
    strokeColor?: string;
    fillColor?: string;
    strokeStyle?: GeoStrokeStyle;
    visible?: boolean;
    highlighted?: boolean;
    selected?: boolean;
}

export interface DefaultGeoRenderProp {
    vertex: VertexGeoRenderProp;
    line: LineGeoRenderProp;
    circle: CircleGeoRenderProp;
    polygon: PolygonGeoRenderProp;
    angle: AngleGeoRenderProp;
}
```

### Specific Render Properties

```typescript
export interface VertexGeoRenderProp extends GeoRenderProp {
    radius?: number;
    shape?: 'circle' | 'square' | 'diamond';
}

export interface LineGeoRenderProp extends GeoRenderProp {
    lineType?: LineType;
    arrowStart?: boolean;
    arrowEnd?: boolean;
}

export interface CircleGeoRenderProp extends GeoRenderProp {
    showCenter?: boolean;
    showRadius?: boolean;
}

export interface PolygonGeoRenderProp extends GeoRenderProp {
    showVertices?: boolean;
    showSides?: boolean;
}
```

## Object Collections

### GeoObjCollection

Manages collections of geometric render elements:

```typescript
export class GeoObjCollection {
    protected readonly _elements: Map<number, GeoRenderElement> = new Map();
    private readonly cachedPotentialSelection: Map<number, GeoRenderElement> = new Map();
    private readonly cachedStrokesByType: Map<GeoRelType, Map<number, StrokeType>> = new Map();
    
    // Element management
    addRenderElement(element: GeoRenderElement): void;
    removeByIds(ids: number[]): void;
    elementAt<T extends GeoRenderElement>(relIndex: number): T | undefined;
    
    // Querying
    vertices(highlighted?: boolean): RenderVertex[];
    lines(highlighted?: boolean): RenderLine[];
    circles(highlighted?: boolean): RenderCircle[];
    polygons(highlighted?: boolean): RenderPolygon[];
    
    // Selection support
    potentialSelectionElements(): GeoRenderElement[];
    strokesByType(type: GeoRelType): Map<number, StrokeType>;
    
    // State management
    highlight(elIndexArr: number[]): void;
    removeHighlight(elIndexArr: number[]): void;
    select(elIndexArr: number[]): void;
}
```

## Gateway Models

### API Communication

```typescript
export interface FetchDocResponse {
    docId: DocumentId;
    numDim: number;
    docRenderProp: DocRenderProp;
    docDefaultElRenderProp: DefaultGeoRenderProp;
    render: GeoRenderElement[];
}

export interface ConstructionRequest {
    construction: GeoElConstructionRequest;
    renderProp?: GeoRenderProp;
}

export interface GeoElConstructionRequest {
    ctId: string;
    elType: GeoObjectType;
    cgName: string;
    paramSpecs?: ParamSpecs[];
    name?: string;
    ctIdx?: number;
}

export interface ApplyConstructionResponse {
    render: GeoRenderElement[];
    elIdxes: number[];
    ctIndexes: number[];
    renameEls: RenameElementModel[];
}
```

### Parameter Specifications

```typescript
export interface ParamSpecs {
    paramKind: ParamKind;
    value?: number;
    name?: string;
    expression?: string;
    relIdx?: number;
    relType?: RelType;
}

export enum ParamKind {
    'value',
    'name',
    'expression',
}
```

## Tool State Models

### Base Tool States

```typescript
export class CommonToolState implements ToolState {}

export class TriangleToolState extends CommonToolState {
    drawMode: number = 0; // 0: by 3 points, 1: by base and height
}

export class QuadToolState extends CommonToolState {
    drawMode: number = 0; // Different modes for quadrilaterals
}

export class RegularPolygonToolState extends CommonToolState {
    sideCount: number = 6;
    drawMode: number = 0; // 0: center and vertex, 1: side
}
```

### Element Management States

```typescript
export interface ElementItem {
    relIdx: number;
    name: string;
    elType: GeoObjectType;
    relType: GeoRelType;
    hidden: boolean;
    highlighted: boolean;
    selected: boolean;
    depIdxes?: number[];
}

export type ElementItemActionType =
    | 'highlight' | 'remove-highlight' | 'select' | 'select-multiple'
    | 'hide' | 'show' | 'delete' | 'setting' | 'deps';

export interface ElementItemAction {
    actionType: ElementItemActionType;
    relIdx: number;
}
```

## Preview System

### Preview Information

```typescript
export interface GeoPreviewInfo {
    refPEl?: GeoRenderElement[]; // Referenced preview elements
    refREl?: GeoRenderElement[]; // Referenced real elements
    previewType?: 'construction' | 'highlight' | 'selection';
}

export class PreviewQueue {
    private queue: GeoRenderElement[] = [];
    
    add(...elements: GeoRenderElement[]): void;
    async flush(docCtrl: GeoDocCtrl): Promise<void>;
    clear(): void;
}
```

### Preview Utilities

```typescript
// Preview element creation
export function pVertex(x: number, y: number, id: number): RenderVertex;
export function pLine(a: number, b: number, c: number, id: number): RenderLine;
export function pCircle(cx: number, cy: number, r: number, id: number): RenderCircle;
export function pAngle(vertex: Point, start: Point, end: Point, id: number): RenderAngle;

// Preview property builders
export function buildPreviewVertexRenderProp(): VertexGeoRenderProp;
export function buildPreviewLineRenderProp(): LineGeoRenderProp;
export function buildPreviewCircleRenderProp(): CircleGeoRenderProp;
```

## Naming Patterns

### Name Validation

```typescript
export const RenderNamePatternModel = {
    RenderVertex: {
        relType: ['Vertex'],
        name: 'Điểm',
        regex: [/^[A-Z]\d*'?$/],
    },
    RenderLine: {
        relType: ['Line', 'LineSegment', 'Vector', 'Ray'],
        name: 'Đường thẳng',
        regex: [/^[a-z]\d*'?$/],
    },
    RenderLineSegment: {
        relType: ['Line', 'LineSegment', 'Vector', 'Ray'],
        name: 'Đoạn thẳng',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    RenderAngle: {
        relType: ['Angle'],
        name: 'Góc',
        regex: [/^([A-Z]\d*'?)$/, /^([A-Za-z]\d*'?)([A-Z]\d*'?)([A-Za-z]\d*'?)$/],
    }
};
```

## Usage Examples

### Creating Render Elements

```typescript
// Create a vertex
const vertex = new RenderVertex(10, 20, 1);
vertex.renderProp = {
    radius: 3,
    strokeColor: 'blue',
    fillColor: 'lightblue'
};

// Create a line
const line = new RenderLine();
line.a = 1; line.b = -1; line.c = 0; // Line equation: x - y = 0
line.relIndex = 2;
line.vertexRelIdxes = [1, 3]; // References to vertices
```

### Working with Collections

```typescript
const collection = new GeoObjCollection();

// Add elements
collection.addRenderElement(vertex);
collection.addRenderElement(line);

// Query elements
const vertices = collection.vertices();
const lines = collection.lines();
const highlighted = collection.vertices(true);

// Manage selection
collection.highlight([1, 2]);
collection.select([1]);
```

### Construction Requests

```typescript
const pointConstruction: GeoElConstructionRequest = {
    ctId: 'Point',
    elType: 'Point',
    cgName: 'PointByCoordinates',
    paramSpecs: [
        { paramKind: ParamKind.value, value: 10 },
        { paramKind: ParamKind.value, value: 20 }
    ]
};

const lineConstruction: GeoElConstructionRequest = {
    ctId: 'Line',
    elType: 'LineVi',
    cgName: 'LineByTwoPoints',
    paramSpecs: [
        { paramKind: ParamKind.name, relIdx: 1, relType: 'Vertex' },
        { paramKind: ParamKind.name, relIdx: 2, relType: 'Vertex' }
    ]
};
```

## Best Practices

1. **Type Safety**: Use TypeScript types consistently
2. **Immutability**: Prefer immutable operations where possible
3. **Validation**: Validate data at boundaries
4. **Performance**: Use efficient data structures for collections
5. **Consistency**: Follow naming conventions and patterns
6. **Documentation**: Document complex model relationships
