# API Reference Documentation

## Overview

This document provides a comprehensive API reference for the Viclass Geometry Editor. It covers the main classes, interfaces, and functions available for extending and integrating with the geometry editor.

## Core APIs

### GeometryEditor

Main editor class that orchestrates all geometry operations.

```typescript
class GeometryEditor extends EditorBase<GeoDocCtrl> {
    // Properties
    readonly geoEditorConf: GeoEditorConfig;
    readonly operationMode: OperationMode;
    readonly geoGateway: GeoGateway;
    
    // Features
    selectionFeature: SelectionFeature;
    panFeature: PanFeature;
    zoomFeature: ZoomFeature;
    awarenessFeature: AwarenessFeature;
    
    // Methods
    createDocument(initData: GeoDocInitData): Promise<GeoDocCtrl>;
    createToolbar(): ToolBar<any, any>;
    start(): Promise<void>;
    filterElementFunc(el: GeoRenderElement): boolean;
}
```

### GeoDocCtrl

Document controller for managing geometric documents.

```typescript
class GeoDocCtrl extends DefaultVDocCtrl implements HasBoundaryCtrl {
    // Properties
    rendererCtrl: GeoRenderer;
    readonly selectedElements$: Observable<GeoRenderElement[]>;
    readonly docRenderProp$: Observable<DocRenderProp>;
    
    // Methods
    attachRenderer(renderer: GeoRenderer): void;
    checkHit(event: LocatableEvent<any>, layer: BoundedGraphicLayerCtrl, 
             useRelaxedHitPrecision?: boolean, preview?: boolean): GeoSelectHitContext;
    updateBoundary(boundary: BoundaryRectangle): void;
    buildGeoRenderDocState(): GeoRenderDocState;
}
```

### GeoRenderer

Abstract base class for geometric renderers.

```typescript
abstract class GeoRenderer {
    // Properties
    readonly docCtrl: GeoDocCtrl;
    readonly layer: GraphicLayerCtrl;
    protected originObjects: GeoObjCollection;
    protected previewObjects: GeoObjCollection;
    
    // Methods
    abstract render(): void;
    abstract clearBoard(): void;
    
    addActualElement(element: GeoRenderElement): void;
    addPreviewElement(element: GeoRenderElement): void;
    removePreviewByIds(previewEls: (GeoRenderElement | number)[]): void;
    elementAt<T extends GeoRenderElement>(relIndex: number): T | undefined;
    
    // Coordinate transformations
    geoToLayerX(x: number): number;
    geoToLayerY(y: number): number;
    layerToGeoX(x: number): number;
    layerToGeoY(y: number): number;
}
```

## Tool APIs

### GeometryTool

Base class for all geometry tools.

```typescript
abstract class GeometryTool<T extends ToolState> extends Tool<T, GeometryToolBar> {
    // Properties
    protected focusDocCtrl: GeoDocCtrl;
    protected editor: GeometryEditor;
    
    // Abstract methods
    abstract onPointerDown(event: GeoPointerEvent): void;
    
    // Optional methods
    onPointerMove?(event: GeoPointerEvent): void;
    onPointerUp?(event: GeoPointerEvent): void;
    onKeyDown?(event: GeoKeyboardEvent): void;
    onActivate?(): void;
    onDeactivate?(): void;
}
```

### Tool State Interfaces

```typescript
interface ToolState {}

class CommonToolState implements ToolState {}

class TriangleToolState extends CommonToolState {
    drawMode: number;
}

class QuadToolState extends CommonToolState {
    drawMode: number;
}

class RegularPolygonToolState extends CommonToolState {
    sideCount: number;
    drawMode: number;
}
```

## Selector APIs

### ElementSelector

Base class for element selectors.

```typescript
abstract class ElementSelector<T extends SelectableType, OT extends SelectorOptions<T>> {
    // Properties
    abstract multiple: boolean;
    selected?: T;
    acceptMarked: boolean;
    
    // Methods
    abstract tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;
    abstract tryPreview(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;
    abstract clearTrial(doc: GeoDocCtrl): void;
    
    trySelect(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;
    accept(el: T, doc: GeoDocCtrl): void;
    reset(keepPreview?: boolean): void;
}
```

### Selector Factory Functions

```typescript
// Basic selectors
function vertex(options?: VertexSelectorOptions): VertexSelector;
function stroke(options?: StrokeSelectorOptions): StrokeSelector;
function vertexOnStroke(options?: VertexOnStrokeSelectorOptions): VertexOnStrokeSelector;

// Multi-element selectors
function or(selectors: ElementSelector<SelectableType>[], 
           options?: MultiSelectorOptions): OrSelector;
function then(selectors: ElementSelector<SelectableType>[], 
             options?: MultiSelectorOptions): ThenSelector;
function repeat(selector: ElementSelector<SelectableType>, 
               options?: RepeatSelectorOptions): RepeatSelector;
```

### Selector Options

```typescript
interface SelectorOptions<T extends SelectableType> {
    preview?: boolean;
    realEl?: boolean;
    autoAccept?: boolean;
    stickyOnMatch?: boolean;
    cursor?: BehaviorSubject<Cursor[]>;
    refinedFilter?: (el: GeoRenderElement) => boolean;
    onComplete?: (selector: ElementSelector<T>, doc: GeoDocCtrl) => void;
    onReset?: (selector: ElementSelector<T>) => void;
}

interface VertexSelectorOptions extends SelectorOptions<RenderVertex> {
    stickyPTypes?: PointerType[];
}

interface StrokeSelectorOptions<T extends SelectableType> extends SelectorOptions<T> {
    selectableStrokeTypes?: GeoRelType[];
    cfunc?: (el: StrokeType, doc: GeoDocCtrl) => boolean;
}

interface RepeatSelectorOptions<T extends SelectableType[]> extends MultiSelectorOptions<T> {
    minCount?: number;
    maxCount?: number;
}
```

## Model APIs

### Render Elements

```typescript
abstract class GeoRenderElement {
    abstract readonly type: GeoRelType;
    abstract readonly elType: GeoObjectType;
    abstract renderProp?: GeoRenderProp;
    
    relIndex: number;
    name: string;
    usable: boolean;
    deleted?: boolean;
    valid: boolean;
    pInfo?: GeoPreviewInfo;
}

class RenderVertex extends GeoRenderElement {
    readonly type: GeoRelType = 'Vertex';
    readonly elType: GeoObjectType = 'Point';
    
    x: number;
    y: number;
    renderProp?: VertexGeoRenderProp;
}

class RenderLine extends GeoRenderElement {
    readonly type: GeoRelType = 'Line';
    readonly elType: GeoObjectType = 'LineVi';
    
    a: number; // Line equation: ax + by + c = 0
    b: number;
    c: number;
    renderProp?: LineGeoRenderProp;
    
    vector(renderer?: GeoRenderer): number[];
    distanceToPoint(x: number, y: number): number;
}
```

### Object Collections

```typescript
class GeoObjCollection {
    // Element management
    addRenderElement(element: GeoRenderElement): void;
    removeByIds(ids: number[]): void;
    elementAt<T extends GeoRenderElement>(relIndex: number): T | undefined;
    
    // Querying
    vertices(highlighted?: boolean): RenderVertex[];
    lines(highlighted?: boolean): RenderLine[];
    circles(highlighted?: boolean): RenderCircle[];
    polygons(highlighted?: boolean): RenderPolygon[];
    allElements(): GeoRenderElement[];
    
    // State management
    highlight(elIndexArr: number[]): void;
    removeHighlight(elIndexArr: number[]): void;
    select(elIndexArr: number[]): void;
    
    // Properties
    readonly size: number;
}
```

## Mathematical APIs

### Complex Numbers

```typescript
class Complex {
    static EPSILON: number;
    
    constructor(re?: number, im?: number);
    
    // Properties
    get real(): number;
    get imag(): number;
    
    // Factory methods
    static fromRI(re: number, im: number): Complex;
    static fromPolar(magnitude: number, angle: number): Complex;
    static fromZ(z: Complex): Complex;
    
    // Arithmetic operations
    add(n: number | Complex): Complex;
    sub(n: number | Complex): Complex;
    mult(n: number | Complex): Complex;
    div(n: number | Complex): Complex;
    
    // Advanced operations
    abs(): number;
    arg(): number;
    pow(n: number): Complex;
    sqrt(): Complex;
    exp(): Complex;
    log(): Complex;
    roots(n: number): Complex[];
    
    // Utility methods
    equals(z: Complex, epsilon?: number): boolean;
    isReal(epsilon?: number): boolean;
    isZero(epsilon?: number): boolean;
    toString(): string;
}
```

### Polynomial Solving

```typescript
abstract class PolyBase {
    static getPoly(coefs: number[]): PolyBase;
    
    roots(): Complex[];
    eval(x: number | Complex): Complex;
}

class Linear extends PolyBase {
    constructor(coefs: number[]); // [a1, a0] for a1*x + a0 = 0
}

class Quadratic extends PolyBase {
    constructor(coefs: number[]); // [a2, a1, a0] for a2*x^2 + a1*x + a0 = 0
}

class Cubic extends PolyBase {
    constructor(coefs: number[]); // [a3, a2, a1, a0]
}

class Quartic extends PolyBase {
    constructor(coefs: number[]); // [a4, a3, a2, a1, a0]
}
```

### Coefficients

```typescript
class Coefficients {
    a: number; b: number; c: number;
    d: number; e: number; f: number;
    
    constructor(a: number, b: number, c: number, d: number, e: number, f: number);
    
    // Operations
    add(other: Coefficients): Coefficients;
    subtract(other: Coefficients): Coefficients;
    mult(scalar: number): Coefficients;
    divide(scalar: number): Coefficients;
    
    // Utilities
    getCoef(name: string): number;
    copy(): Coefficients;
    
    // Static methods
    static elliminateTerm(s: Coefficients, t: Coefficients, coef: string): Coefficients;
}
```

## Direction APIs

### Direction Calculation Functions

```typescript
// Line-based direction
function nthDirectionByLine(vec: number[], role: number[], p: number[]): number;
function nthSideOfVector(ps: Point[], p1Idx: number, p2Idx: number, p3Idx: number, 
                        p1RelIdx: number, p2RelIdx: number): number;

// Rotational direction
function nthDirectionRotation(vec: number[], role: number[], p0: number[], 
                             points: number[][]): number;
function sortByRotationV2(role: Point, points: Point[], vec?: Vector): Point[];

// Linear direction
function nthDirectionOnLine(vec: number[], p0: number[], points: number[][]): number;

// Direction comparison
function directionOfPointOnParallelVector(vec: number[], root: number[], p: number[]): 1 | -1;
function isLargerThan(sourceRelIdx: number, targetRelIdx: number): boolean;
```

## Gateway APIs

### GeoGateway

API communication gateway for geometric operations.

```typescript
class GeoGateway {
    constructor(backendUri: string);
    
    // Document operations
    createDoc(numDim: number, width: number, height: number, unit: number): Promise<FetchDocResponse>;
    fetchDoc(docGlobalId: DocumentId): Promise<FetchDocResponse>;
    
    // Construction operations
    construct(docGlobalId: DocumentId, constructions: ConstructionRequest[], 
             preview?: boolean): Promise<ApplyConstructionResponse>;
    reconstruct(docGlobalId: DocumentId, ctIdx: number, 
               construction: GeoElConstructionRequest): Promise<ReconstructionResponse>;
    
    // Element operations
    moveElement(docGlobalId: DocumentId, ctIdx: number, 
               construction: GeoElConstructionRequest): Promise<MoveElementResponse>;
    updateElementState(docGlobalId: DocumentId, 
                      request: UpdateElementStateRequest): Promise<void>;
    renameElement(docGlobalId: DocumentId, 
                 renameModel: RenameElementModel): Promise<void>;
    
    // Utility operations
    nameByLang(textIds: string[]): Promise<Map<string, string>>;
    constraintTemplates(template: string): Promise<ConstraintTemplateData>;
}
```

### Construction Models

```typescript
interface GeoElConstructionRequest {
    ctId: string;
    elType: GeoObjectType;
    cgName: string;
    paramSpecs?: ParamSpecs[];
    name?: string;
    ctIdx?: number;
}

interface ConstructionRequest {
    construction: GeoElConstructionRequest;
    renderProp?: GeoRenderProp;
}

interface ParamSpecs {
    paramKind: ParamKind;
    value?: number;
    name?: string;
    expression?: string;
    relIdx?: number;
    relType?: RelType;
}

enum ParamKind {
    'value',
    'name',
    'expression',
}
```

## Utility APIs

### Preview Utilities

```typescript
class PreviewQueue {
    add(...elements: GeoRenderElement[]): void;
    flush(docCtrl: GeoDocCtrl): Promise<void>;
    clear(): void;
}

// Preview element creation
function pVertex(x: number, y: number, id: number): RenderVertex;
function pLine(a: number, b: number, c: number, id: number): RenderLine;
function pCircle(cx: number, cy: number, r: number, id: number): RenderCircle;
function pAngle(vertex: Point, start: Point, end: Point, id: number): RenderAngle;
```

### Tool Utilities

```typescript
// Construction utilities
function remoteConstruct(editor: GeometryEditor, constructions: ConstructionRequest[], 
                        docCtrl: GeoDocCtrl): Promise<ApplyConstructionResponse>;
function buildPointConstruction(vertex: RenderVertex): GeoElConstructionRequest;
function buildLineConstruction(vertices: RenderVertex[]): GeoElConstructionRequest;

// Coordinate utilities
function calculatePosInLayer(event: LocatableEvent<any>, docCtrl: GeoDocCtrl): Position;
function validatePointerPos(pos: Position, docCtrl: GeoDocCtrl): boolean;

// Naming utilities
function assignNames(elements: GeoRenderElement[], pattern: string): void;
function validateName(name: string, elementType: GeoRelType): boolean;
```

## Type Definitions

### Core Types

```typescript
type GeoObjectType = 'Point' | 'Line' | 'LineSegment' | 'Ray' | 'Vector' | 
                    'Circle' | 'Ellipse' | 'Semicircle' | 'CircularSector' |
                    'Triangle' | 'RightTriangle' | 'IsoscelesTriangle' | 'EquilateralTriangle' |
                    'Polygon' | 'RegularPolygon' | 'Quadrilateral' |
                    'Rectangle' | 'Square' | 'Parallelogram' | 'Rhombus' | 'Trapezoid' |
                    'Angle';

type GeoRelType = 'Vertex' | 'Line' | 'LineSegment' | 'Vector' | 'Ray' |
                 'Circle' | 'CircleShape' | 'Ellipse' | 'EllipseShape' |
                 'Sector' | 'SectorShape' | 'Polygon' | 'Angle';

type GeometryToolType = 'CreatePointTool' | 'CreateLineTool' | 'CreateCircleTool' |
                       'CreateTriangleTool' | 'CreateSquareTool' | 'CreateAngleTool' |
                       'IntersectionPointTool' | 'MiddlePointTool' | 'PointOnObjectTool' |
                       'CreateParallelLineTool' | 'CreatePerpendicularLineTool' |
                       'MoveElementTool' | 'UpdatePropTool' | 'RenameElementTool' |
                       'GeoPanTool' | 'GeoZoomTool' | 'InputCommandTool';

type SelectableType = GeoRenderElement | GeoRenderElement[] | SelectableType[] | 'nothing';
```

### Event Types

```typescript
type GeoPointerEvent = UIPointerEventData<NativeEventTarget<any>>;
type GeoKeyboardEvent = KeyboardEventData<NativeEventTarget<any>>;
type GeoToolEventData = ToolEventData<GeometryToolBar, GeometryToolType>;
```

## Constants

```typescript
const GeoEpsilon = Math.pow(10, -10);
const RENDER_VERTEX_RADIUS = 3;
const RENDER_VERTEX_RADIUS_POTENTIAL = 5;

const PreviewIds = {
    stroke: -1000000,
    vertex: -10000000,
};
```

This API reference provides the essential interfaces and classes for extending the Viclass Geometry Editor. For detailed implementation examples, refer to the specific module documentation files.
