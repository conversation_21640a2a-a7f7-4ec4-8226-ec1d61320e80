# Tài Liệu Hệ Thống Công Cụ

## Tổng Quan

Hệ thống công cụ cung cấp một bộ công cụ toàn diện để tạo và thao tác hình học. Mỗi công cụ tuân theo một mẫu nhất quán và tích hợp với hệ thống selector để tương tác với phần tử.

## Kiến Trúc Công Cụ

### Lớp Công Cụ Cơ Sở

Tất cả các công cụ hình học đều mở rộng lớp cơ sở `GeometryTool`:

```typescript
export abstract class GeometryTool<T extends ToolState> extends Tool<T, GeometryToolBar> {
    protected focusDocCtrl: GeoDocCtrl;
    protected editor: GeometryEditor;
    
    // Bộ xử lý sự kiện
    abstract onPointerDown(event: GeoPointerEvent): void;
    onPointerMove?(event: GeoPointerEvent): void;
    onPointerUp?(event: GeoPointerEvent): void;
    onKeyDown?(event: GeoKeyboardEvent): void;
}
```

### Quản Lý Trạng Thái Công Cụ

Mỗi công cụ duy trì trạng thái của nó thông qua một đối tượng `ToolState`:

```typescript
export class CommonToolState implements ToolState {}

export class TriangleToolState extends CommonToolState {
    drawMode: number = 0; // 0: bằng 3 điểm, 1: bằng đáy và chiều cao
}

export class QuadToolState extends CommonToolState {
    drawMode: number = 0; // Các chế độ khác nhau cho các tứ giác khác nhau
}
```

## Công Cụ Điểm

### CreatePointTool

Tạo điểm tại các vị trí được chỉ định:

```typescript
export class CreatePointTool extends GeometryTool<CommonToolState> {
    private selector = vertex({ preview: true, autoAccept: true });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (selected) {
            this.createPoint(selected);
        }
    }
}
```

### MiddlePointTool

Tạo điểm giữa giữa hai điểm:

```typescript
export class MiddlePointTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertex({ preview: true }), { 
        minCount: 2, 
        maxCount: 2 
    });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createMiddlePoint(selected);
        }
    }
}
```

### IntersectionPointTool

Tạo điểm giao giữa các đối tượng hình học:

```typescript
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    private selector = repeat(stroke({
        selectableStrokeTypes: ['RenderLine', 'RenderCircle', 'RenderEllipse']
    }), { minCount: 2, maxCount: 2 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createIntersectionPoints(selected);
        }
    }
}
```

## Công Cụ Đường

### CreateLineTool

Tạo đường thẳng vô hạn qua hai điểm:

```typescript
export class CreateLineTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createLine(selected);
        }
    }
}
```

### CreateLineSegmentTool

Tạo đoạn thẳng giữa hai điểm:

```typescript
export class CreateLineSegmentTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createLineSegment(selected);
        }
    }
}
```

### CreateParallelLineTool

Tạo đường thẳng song song với đường thẳng hiện có:

```typescript
export class CreateParallelLineTool extends GeometryTool<CommonToolState> {
    private selector = then([
        stroke({ selectableStrokeTypes: ['RenderLine'] }),
        vertex({ preview: true })
    ]);
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createParallelLine(selected);
        }
    }
}
```

## Công Cụ Hình

### CreateCircleTool

Tạo đường tròn với tâm và bán kính:

```typescript
export class CreateCircleTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createCircle(selected);
        }
    }
}
```

### CreateTriangleTool

Tạo tam giác với các chế độ khác nhau:

```typescript
export class CreateTriangleTool extends GeometryTool<TriangleToolState> {
    private selector = repeat(vertexS(), { minCount: 3, maxCount: 3 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createTriangle(selected);
        }
    }
}
```

### CreateSquareTool

Tạo hình vuông với logic xây dựng đặc biệt:

```typescript
export class CreateSquareTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createSquare(selected);
        }
    }
    
    private createSquare(vertices: SelectedVertex[]): void {
        const [v1, v2] = vertices;
        // Tính toán các đỉnh hình vuông sử dụng thuật toán hướng
        const direction = nthDirectionByLine(
            [v2.x - v1.x, v2.y - v1.y], 
            [v1.x, v1.y], 
            [v2.x, v2.y]
        );
        // Tạo xây dựng hình vuông...
    }
}
```

## Công Cụ Biến Đổi

### MoveElementTool

Di chuyển các phần tử hình học:

```typescript
export class MoveElementTool extends GeometryTool<CommonToolState> {
    private selector = or([
        vertex({ realEl: true }),
        stroke({ selectableStrokeTypes: ['RenderLine', 'RenderCircle'] })
    ]);
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (selected) {
            this.startMove(selected, event);
        }
    }
}
```

## Mẫu Xây Dựng Công Cụ

### Yêu Cầu Xây Dựng

Các công cụ tạo đối tượng hình học thông qua yêu cầu xây dựng:

```typescript
interface GeoElConstructionRequest {
    ctId: string;
    elType: GeoObjectType;
    cgName: string;
    paramSpecs?: ParamSpecs[];
}

// Ví dụ: Xây dựng điểm
const pointConstruction: GeoElConstructionRequest = {
    ctId: 'Point',
    elType: 'Point',
    cgName: 'PointByCoordinates',
    paramSpecs: [
        { paramKind: 'value', value: x },
        { paramKind: 'value', value: y }
    ]
};
```

### Xây Dựng Từ Xa

Các công cụ sử dụng xây dựng từ xa để xác thực phía máy chủ:

```typescript
async function remoteConstruct(
    editor: GeometryEditor,
    constructions: ConstructionRequest[],
    docCtrl: GeoDocCtrl
): Promise<ApplyConstructionResponse> {
    return await editor.geoGateway.construct(
        docCtrl.state.globalId,
        constructions
    );
}
```

## Tích Hợp Công Cụ

### Tích Hợp Selector

Các công cụ tích hợp với hệ thống selector:

```typescript
export class ExampleTool extends GeometryTool<CommonToolState> {
    // Định nghĩa selector cho các chế độ tương tác khác nhau
    private pointSelector = vertex({ preview: true });
    private lineSelector = stroke({ selectableStrokeTypes: ['RenderLine'] });
    private combinedSelector = or([this.pointSelector, this.lineSelector]);
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.combinedSelector.trySelect(event, this.focusDocCtrl);
        if (selected) {
            this.handleSelection(selected);
        }
    }
}
```

## Vòng Đời Công Cụ

1. **Khởi Tạo**: Công cụ được tạo và thêm vào thanh công cụ
2. **Kích Hoạt**: Công cụ trở nên hoạt động khi được chọn
3. **Tương Tác**: Công cụ xử lý các sự kiện con trỏ và bàn phím
4. **Xây Dựng**: Công cụ tạo các đối tượng hình học
5. **Hủy Kích Hoạt**: Công cụ bị hủy kích hoạt khi công cụ khác được chọn

## Thực Hành Tốt Nhất

1. **Sử Dụng Selector**: Luôn sử dụng DSL selector để lựa chọn phần tử
2. **Quản Lý Trạng Thái**: Duy trì trạng thái công cụ đúng cách
3. **Phần Tử Preview**: Cung cấp phản hồi trực quan trong quá trình xây dựng
4. **Xử Lý Lỗi**: Xử lý các xây dựng không hợp lệ một cách nhẹ nhàng
5. **Hiệu Suất**: Tối ưu hóa cho tương tác mượt mà
6. **Nhất Quán**: Tuân theo các mẫu đã thiết lập cho các công cụ tương tự
