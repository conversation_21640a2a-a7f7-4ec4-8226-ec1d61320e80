# Tài Liệu <PERSON>ệ Thống Renderer

## Tổng <PERSON>uan

<PERSON>ệ thống renderer cung cấp render canvas 2D hiệu suất cao cho các đối tượng hình học. Nó xử lý biến đổi tọa độ, quản lý viewport, render đối tượng và phản hồi trực quan cho trình soạn thảo hình học.

## Kiến Trúc

### Các Thành Phần Renderer

```
renderer/
├── geo.renderer.ts         # Lớp renderer cơ sở
├── geo2d.renderer.ts      # Triển khai renderer canvas 2D
└── geo2d.renderer.utils.ts # Tiện ích và helper render
```

## Renderer Cơ Sở

### Lớp GeoRenderer

Lớp cơ sở trừu tượng cho tất cả renderer hình học:

```typescript
export abstract class GeoRenderer {
    // Thuộc tính cốt lõi
    public readonly docCtrl: GeoDocCtrl;
    public readonly layer: GraphicLayerCtrl;
    protected readonly viewport: BoardViewportManager;
    protected readonly canvas: HTMLCanvasElement;
    
    // Bộ sưu tập đối tượng
    protected originObjects: GeoObjCollection;
    protected previewObjects: GeoObjCollection;
    protected potentialSelection: GeoRenderElement[] = [];
    
    // Biến đổi tọa độ
    protected currLookAt: Position;
    protected zoomUnit: number;
    
    constructor(docCtrl: GeoDocCtrl, layer: GraphicLayerCtrl) {
        this.docCtrl = docCtrl;
        this.layer = layer;
        this.viewport = docCtrl.viewport as BoardViewportManager;
        this.canvas = layer.canvas;
        this.clearState();
    }
    
    abstract render(): void;
    abstract clearBoard(): void;
}
```

### Quản Lý Đối Tượng

```typescript
// Quản lý phần tử
addActualElement(element: GeoRenderElement): void {
    this.originObjects.addRenderElement(element);
}

addPreviewElement(element: GeoRenderElement): void {
    this.previewObjects.addRenderElement(element);
}

removePreviewByIds(previewEls: (GeoRenderElement | number)[]): void {
    const ids = previewEls.map(preview => 
        typeof preview === 'number' ? preview : preview.relIndex
    );
    this.previewObjects.removeByIds(ids);
    this.onElementChange.emit(new DefaultEventData('elementChange', previewEls));
}

// Truy cập phần tử
elementAt<T extends GeoRenderElement>(relIndex: number): T | undefined {
    return relIndex >= 0 ? 
        this.originObjects.elementAt(relIndex) : 
        this.previewObjects.elementAt(relIndex);
}

realElAt<T extends GeoRenderElement>(relIndex: number): T {
    return this.originObjects.elementAt(relIndex) as T;
}

previewElAt<T extends GeoRenderElement>(relIndex: number): T | undefined {
    return this.previewObjects.elementAt(relIndex) as T;
}
```

### Biến Đổi Tọa Độ

```typescript
// Chuyển đổi tọa độ hình học sang tọa độ lớp
geoToLayerX(x: number): number {
    return (x + this.currLookAt.x) * this.zoomUnit;
}

geoToLayerY(y: number): number {
    return (y + this.currLookAt.y) * this.zoomUnit;
}

// Chuyển đổi tọa độ lớp sang tọa độ hình học
layerToGeoX(x: number): number {
    return x / this.zoomUnit - this.currLookAt.x;
}

layerToGeoY(y: number): number {
    return y / this.zoomUnit - this.currLookAt.y;
}

// Quản lý viewport
lookAt(xInGeoCoord: number, yInGeoCoord: number): void {
    const docRenderProp = this.docCtrl.state.docRenderProp;
    docRenderProp.translation[0] = xInGeoCoord;
    docRenderProp.translation[1] = yInGeoCoord;
    
    if (this.zoomingToLevel && this.zoomingToLevel != docRenderProp.scale) {
        docRenderProp.scale = this.zoomingToLevel;
        delete this.zoomingToLevel;
    }
    
    this.docCtrl.rendererCtrl.render();
}
```

## Renderer Canvas 2D

### Lớp Geo2dRenderer

Triển khai cụ thể cho render canvas 2D:

```typescript
export class Geo2dRenderer extends GeoRenderer {
    declare context: CanvasRenderingContext2D;
    
    private highlightRefEls: Map<'Vertex' | 'Line' | 'Arc', Map<number, GeoRenderElement>>;
    private pendingRenderlabels: PendingRenderLabel[] = [];
    private renderedlabels: RenderedLabel[] = [];
    private checkOverlapStateList: GeoRenderElement[] = [];
    
    constructor(layer: GraphicLayerCtrl, doc: GeoDocCtrl) {
        super(doc, layer);
        this.context = layer.canvas.getContext('2d');
        layer.attachRenderer((vpm: ViewportManager, lc: GraphicLayerCtrl) => this.render());
    }
}
```

### Pipeline Render

```typescript
override render(): void {
    this.clearBoard();
    
    // Đặt lại trạng thái render
    this.checkOverlapStateList = [];
    this.renderedlabels = [];
    this.pendingRenderlabels = [];
    
    // Render lưới và trục
    this.renderGrid();
    
    // Render đối tượng hình học
    this.renderObjects(false); // Đối tượng thực
    this.renderObjects(true);  // Đối tượng được highlight
    
    // Render nhãn
    this.renderLabels();
    
    // Render lựa chọn tiềm năng
    this.renderPotentialSelections();
}

private renderObjects(highlight: boolean): void {
    const state = highlight ? this.originObjects : this.originObjects;
    
    // Lấy đối tượng theo loại, sắp xếp theo ưu tiên render
    const shapes = state.polygons(highlight);
    const lines = state.lines(highlight);
    const circles = state.circles(highlight);
    const vertices = state.vertices(highlight);
    const angles = state.angles(highlight);
    
    // Render theo thứ tự: hình, đường, tròn, đỉnh, góc
    for (const obj of shapes) this.renderObject(obj, highlight);
    for (const obj of lines) this.renderObject(obj, highlight);
    for (const obj of circles) this.renderObject(obj, highlight);
    for (const obj of vertices) this.renderObject(obj, highlight);
    for (const obj of angles) this.renderObject(obj, highlight);
}
```

### Render Đối Tượng

```typescript
private renderObject(rel: GeoRenderElement, highlight: boolean = false): void {
    switch (rel.type) {
        case 'RenderVertex':
            this.renderVertex(rel as RenderVertex, highlight);
            break;
        case 'RenderLine':
        case 'RenderLineSegment':
        case 'RenderVector':
        case 'RenderRay':
            this.renderLine(rel as RenderLine, highlight);
            break;
        case 'RenderCircle':
            this.renderCircle(rel as RenderCircle, highlight);
            break;
        case 'RenderCircleShape':
            this.renderCircleShape(rel as RenderCircleShape, highlight);
            break;
        case 'RenderPolygon':
            this.renderPolygon(rel as RenderPolygon, highlight);
            break;
        case 'RenderAngle':
            this.renderAngle(rel as RenderAngle, highlight);
            break;
        case 'RenderEllipse':
            this.renderEllipse(rel as RenderEllipse, highlight);
            break;
        case 'RenderSector':
            this.renderSector(rel as RenderSector, highlight);
            break;
        default:
            break;
    }
}
```

### Phương Thức Render Cụ Thể

#### Render Vertex

```typescript
private renderVertex(vertex: RenderVertex, highlight: boolean): void {
    const x = this.geoToLayerX(vertex.x);
    const y = this.geoToLayerY(vertex.y);
    const renderProp = vertex.renderProp || this.docCtrl.docDefaultElRenderProps.vertex;
    
    const radius = highlight ? 
        RENDER_VERTEX_RADIUS_POTENTIAL : 
        (renderProp.radius || RENDER_VERTEX_RADIUS);
    
    const strokeColor = highlight ? 
        'orange' : 
        (renderProp.strokeColor || 'black');
    
    const fillColor = renderProp.fillColor || 'white';
    
    this.context.save();
    this.context.beginPath();
    this.context.arc(x, y, radius, 0, 2 * Math.PI);
    
    // Tô màu
    if (fillColor) {
        this.context.fillStyle = fillColor;
        this.context.fill();
    }
    
    // Viền
    this.context.strokeStyle = strokeColor;
    this.context.lineWidth = renderProp.lineWeight || 1;
    this.context.stroke();
    
    this.context.restore();
    
    // Thêm nhãn nếu cần
    if (vertex.name) {
        this.addPendingLabel(vertex.name, new DOMPoint(x, y), vertex, highlight);
    }
}
```

#### Render Line

```typescript
private renderLine(line: RenderLine, highlight: boolean): void {
    const renderProp = line.renderProp || this.docCtrl.docDefaultElRenderProps.line;
    const strokeColor = highlight ? 'orange' : (renderProp.strokeColor || 'black');
    const lineWeight = renderProp.lineWeight || 1;
    
    this.context.save();
    this.context.strokeStyle = strokeColor;
    this.context.lineWidth = lineWeight;
    
    if (renderProp.strokeStyle?.lineDash) {
        this.context.setLineDash(renderProp.strokeStyle.lineDash);
    }
    
    this.context.beginPath();
    
    switch (line.type) {
        case 'RenderLine':
            this.renderInfiniteLine(line);
            break;
        case 'RenderLineSegment':
            this.renderLineSegment(line as RenderLineSegment);
            break;
        case 'RenderRay':
            this.renderRay(line as RenderRay);
            break;
        case 'RenderVector':
            this.renderVector(line as RenderVector);
            break;
    }
    
    this.context.stroke();
    this.context.restore();
}

private renderLineSegment(segment: RenderLineSegment): void {
    const startPoint = segment.startPoint(this);
    const endPoint = segment.endPoint(this);
    
    const x1 = this.geoToLayerX(startPoint.x);
    const y1 = this.geoToLayerY(startPoint.y);
    const x2 = this.geoToLayerX(endPoint.x);
    const y2 = this.geoToLayerY(endPoint.y);
    
    this.context.moveTo(x1, y1);
    this.context.lineTo(x2, y2);
}
```

#### Render Circle

```typescript
private renderCircle(circle: RenderCircle, highlight: boolean): void {
    const centerX = this.geoToLayerX(circle.centerX);
    const centerY = this.geoToLayerY(circle.centerY);
    const radius = circle.radius * this.zoomUnit;
    
    const renderProp = circle.renderProp || this.docCtrl.docDefaultElRenderProps.circle;
    const strokeColor = highlight ? 'orange' : (renderProp.strokeColor || 'black');
    
    this.context.save();
    this.context.beginPath();
    this.context.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    
    this.context.strokeStyle = strokeColor;
    this.context.lineWidth = renderProp.lineWeight || 1;
    this.context.stroke();
    
    this.context.restore();
}

private renderCircleShape(circleShape: RenderCircleShape, highlight: boolean): void {
    // Render viền đường tròn
    this.renderCircle(circleShape, highlight);
    
    // Tô màu nếu được chỉ định
    const renderProp = circleShape.renderProp;
    if (renderProp?.fillColor) {
        const centerX = this.geoToLayerX(circleShape.centerX);
        const centerY = this.geoToLayerY(circleShape.centerY);
        const radius = circleShape.radius * this.zoomUnit;
        
        this.context.save();
        this.context.beginPath();
        this.context.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        this.context.fillStyle = renderProp.fillColor;
        this.context.fill();
        this.context.restore();
    }
}
```

### Render Lưới

```typescript
private renderGrid(): void {
    if (!this.viewport.currentLookAt) return;
    
    const scale = this.getScale();
    const spacing = calculateSpacing(this.docCtrl, this.viewport);
    
    const leftGeo = this.layerToGeoX(this.left);
    const rightGeo = this.layerToGeoX(this.right);
    const topGeo = this.layerToGeoY(this.top);
    const bottomGeo = this.layerToGeoY(this.bottom);
    
    // Render đường lưới
    this.renderGridLines(leftGeo, rightGeo, topGeo, bottomGeo, spacing);
    
    // Render trục
    this.renderAxes(leftGeo, rightGeo, topGeo, bottomGeo);
    
    // Render nhãn lưới
    this.renderGridLabels(leftGeo, rightGeo, topGeo, bottomGeo, spacing);
}

private renderGridLines(left: number, right: number, top: number, bottom: number, spacing: number): void {
    this.context.save();
    this.context.strokeStyle = LineColor.Grid;
    this.context.lineWidth = LineWeight.Grid;
    
    // Đường dọc
    const startX = Math.floor(left / spacing) * spacing;
    for (let x = startX; x <= right; x += spacing) {
        const layerX = this.geoToLayerX(x);
        this.context.beginPath();
        this.context.moveTo(layerX, this.geoToLayerY(top));
        this.context.lineTo(layerX, this.geoToLayerY(bottom));
        this.context.stroke();
    }
    
    // Đường ngang
    const startY = Math.floor(bottom / spacing) * spacing;
    for (let y = startY; y <= top; y += spacing) {
        const layerY = this.geoToLayerY(y);
        this.context.beginPath();
        this.context.moveTo(this.geoToLayerX(left), layerY);
        this.context.lineTo(this.geoToLayerX(right), layerY);
        this.context.stroke();
    }
    
    this.context.restore();
}
```

### Render Nhãn

```typescript
private addPendingLabel(text: string, position: DOMPoint, object: GeoRenderElement, highlight?: boolean): void {
    this.pendingRenderlabels.push({
        text: text,
        vertexPoint: position,
        object: object,
        highlight: highlight
    });
}

private renderLabels(): void {
    for (const label of this.pendingRenderlabels) {
        const fontSize = 12;
        const metrics = getLabelMeasure(this.context, label.text, fontSize);
        
        // Tính toán vị trí nhãn để tránh chồng lấp
        const position = this.calculateLabelPosition(
            label.vertexPoint, 
            metrics.width, 
            fontSize,
            label.object
        );
        
        // Render nhãn
        drawLabel(this.context, label.text, position, {
            fontSize: fontSize,
            textColor: label.highlight ? 'orange' : 'black'
        });
        
        // Theo dõi nhãn đã render để phát hiện chồng lấp
        this.renderedlabels.push({
            bottomLeftPoint: position,
            width: metrics.width,
            height: fontSize
        });
    }
}
```

## Tiện Ích Render

### Hàm Vẽ

```typescript
// Vẽ đường thẳng
export function drawLine(
    ctx: CanvasRenderingContext2D,
    x1: number, y1: number, x2: number, y2: number,
    options: {
        color?: string;
        lineWeight?: number;
        lineDash?: number[];
        withoutSave?: boolean;
    }
): void {
    const color = options?.color ?? 'black';
    const lineWeight = options?.lineWeight ?? 1;
    const withoutSave = options?.withoutSave ?? false;
    
    if (!withoutSave) ctx.save();
    ctx.beginPath();
    
    if (options?.lineDash?.length) ctx.setLineDash(options.lineDash);
    ctx.lineWidth = lineWeight;
    ctx.strokeStyle = color;
    
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();
    
    ctx.closePath();
    if (!withoutSave) ctx.restore();
}

// Vẽ cung
export function drawArc(
    ctx: CanvasRenderingContext2D,
    centerX: number, centerY: number, radius: number,
    startAngle: number, endAngle: number,
    options: {
        color?: string;
        lineWeight?: number;
        fillColor?: string;
    }
): void {
    ctx.save();
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
    
    if (options.fillColor) {
        ctx.fillStyle = options.fillColor;
        ctx.fill();
    }
    
    if (options.color) {
        ctx.strokeStyle = options.color;
        ctx.lineWidth = options.lineWeight || 1;
        ctx.stroke();
    }
    
    ctx.restore();
}
```

### Tiện Ích Nhãn

```typescript
export function getLabelMeasure(
    ctx: CanvasRenderingContext2D, 
    content: string, 
    fontSize: number
): TextMetrics {
    ctx.save();
    ctx.font = fontSize + 'px Montserrat';
    const metrics = ctx.measureText(content);
    ctx.restore();
    return metrics;
}

export function drawLabel(
    ctx: CanvasRenderingContext2D,
    content: string,
    bottomLeft: Position,
    options?: {
        fontSize?: number;
        textColor?: string;
        radAngleFromXAxis?: number;
        flipVertical?: boolean;
        flipHorizontal?: boolean;
        padding?: { top?: number; left?: number; };
    }
): void {
    const fontSize = options?.fontSize ?? 12;
    const textColor = options?.textColor ?? 'black';
    const padding = options?.padding ?? { top: 2, left: 2 };
    
    ctx.save();
    ctx.font = fontSize + 'px Montserrat';
    ctx.fillStyle = textColor;
    ctx.textBaseline = 'bottom';
    
    const x = bottomLeft.x + (padding.left || 0);
    const y = bottomLeft.y - (padding.top || 0);
    
    if (options?.radAngleFromXAxis) {
        ctx.translate(x, y);
        ctx.rotate(options.radAngleFromXAxis);
        ctx.fillText(content, 0, 0);
    } else {
        ctx.fillText(content, x, y);
    }
    
    ctx.restore();
}
```

## Tối Ưu Hóa Hiệu Suất

### Viewport Culling

```typescript
private isElementVisible(element: GeoRenderElement): boolean {
    const bounds = this.getElementBounds(element);
    const viewportBounds = this.getViewportBounds();
    return bounds.intersects(viewportBounds);
}

private renderVisibleElements(): void {
    const visibleElements = this.originObjects.allElements()
        .filter(el => this.isElementVisible(el));
    
    for (const element of visibleElements) {
        this.renderObject(element);
    }
}
```

### Lập Lịch Render

```typescript
scheduleRender(): void {
    if (this.requestedRenderFrame) return;
    
    requestAnimationFrame(() => {
        this.requestedRenderFrame = false;
        this.layer.renderer(this.layer.viewport, this.layer);
    });
    
    this.requestedRenderFrame = true;
}
```

### Tối Ưu Hóa Canvas

```typescript
private useCanvasCoord(): void {
    this.context.setTransform(1, 0, 0, 1, 0, 0);
}

override clearBoard(): void {
    this.context.save();
    this.useCanvasCoord();
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.context.restore();
}
```

## Thực Hành Tốt Nhất

1. **Thứ Tự Render**: Render hình trước đường, đường trước điểm
2. **Quản Lý Trạng Thái**: Luôn save/restore trạng thái canvas
3. **Hiệu Suất**: Sử dụng viewport culling cho số lượng lớn đối tượng
4. **Hệ Tọa Độ**: Sử dụng nhất quán các phương thức biến đổi tọa độ
5. **Phản Hồi Trực Quan**: Cung cấp phản hồi trực quan rõ ràng cho tương tác
6. **Quản Lý Nhãn**: Tránh chồng lấp nhãn và cung cấp văn bản dễ đọc
7. **Quản Lý Bộ Nhớ**: Dọn dẹp tài nguyên khi renderer bị xóa
