# Selector System Documentation

## Overview

The selector system provides a powerful Domain Specific Language (DSL) for describing complex element selection logic in geometry tools. It ensures consistent selection behavior across different tools and allows for sophisticated interaction patterns.

## Architecture

### Base Selector Class

All selectors extend the `ElementSelector` base class:

```typescript
export abstract class ElementSelector<T extends SelectableType, OT extends SelectorOptions<T>> {
    abstract multiple: boolean;
    selected?: T;
    acceptMarked: boolean = false;
    
    abstract tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;
    abstract tryPreview(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;
    abstract clearTrial(doc: GeoDocCtrl): void;
}
```

### Selector Options

Selectors are configured through options:

```typescript
export interface SelectorOptions<T extends SelectableType> {
    preview?: boolean;           // Enable preview elements
    realEl?: boolean;           // Enable real elements
    autoAccept?: boolean;       // Auto-accept on selection
    stickyOnMatch?: boolean;    // Stick to matched element
    cursor?: BehaviorSubject<Cursor[]>;
    refinedFilter?: (el: GeoRenderElement) => boolean;
    onComplete?: (selector: ElementSelector<T>, doc: GeoDocCtrl) => void;
    onReset?: (selector: ElementSelector<T>) => void;
}
```

## Basic Selectors

### Vertex Selector

Selects point elements:

```typescript
export function vertex(options?: VertexSelectorOptions): VertexSelector {
    return new VertexSelector(options);
}

// Usage examples
const pointSelector = vertex({ preview: true, autoAccept: true });
const realPointSelector = vertex({ realEl: true });
const stickyPointSelector = vertex({ stickyOnMatch: true });
```

### Stroke Selector

Selects line-based elements:

```typescript
export function stroke(options?: StrokeSelectorOptions): StrokeSelector {
    return new StrokeSelector(options);
}

// Usage examples
const lineSelector = stroke({ 
    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment'] 
});
const circleSelector = stroke({ 
    selectableStrokeTypes: ['RenderCircle'] 
});
const anyStrokeSelector = stroke(); // All stroke types
```

### Vertex on Stroke Selector

Selects points on geometric curves:

```typescript
export function vertexOnStroke(options?: VertexOnStrokeSelectorOptions): VertexOnStrokeSelector {
    return new VertexOnStrokeSelector(options);
}

// Usage example
const pointOnLineSelector = vertexOnStroke({
    selectableStrokeTypes: ['RenderLine'],
    tfunc: (stroke, vertex, doc) => {
        // Transform vertex position on stroke
        return transformedVertex;
    },
    showPosHint: true
});
```

## Multi-Element Selectors

### Or Selector

Selects one of multiple element types:

```typescript
export function or(
    selectors: ElementSelector<SelectableType>[], 
    options?: MultiSelectorOptions
): OrSelector {
    return new OrSelector(selectors, options);
}

// Usage examples
const pointOrLineSelector = or([
    vertex({ preview: true }),
    stroke({ selectableStrokeTypes: ['RenderLine'] })
], { flatten: true });

const anyElementSelector = or([
    vertex({ realEl: true }),
    stroke({ selectableStrokeTypes: ['RenderLine', 'RenderCircle'] })
]);
```

### Then Selector

Sequential selection of elements:

```typescript
export function then(
    selectors: ElementSelector<SelectableType>[],
    options?: MultiSelectorOptions
): ThenSelector {
    return new ThenSelector(selectors, options);
}

// Usage examples
const lineFromPointsSelector = then([
    vertex({ preview: true }),
    vertex({ preview: true })
]);

const perpendicularLineSelector = then([
    stroke({ selectableStrokeTypes: ['RenderLine'] }),
    vertex({ preview: true })
]);
```

### Repeat Selector

Repeated selection with count constraints:

```typescript
export function repeat(
    selector: ElementSelector<SelectableType>,
    options?: RepeatSelectorOptions
): RepeatSelector {
    return new RepeatSelector(selector, options);
}

// Usage examples
const triangleVerticesSelector = repeat(vertex({ preview: true }), {
    minCount: 3,
    maxCount: 3
});

const polygonVerticesSelector = repeat(vertex({ preview: true }), {
    minCount: 3,
    maxCount: 10
});
```

## Advanced Selector Patterns

### Conditional Selection

Complex conditional logic:

```typescript
const conditionalSelector = or([
    // First try to select existing points
    vertex({ realEl: true }),
    // Then allow creating new points
    vertex({ preview: true })
], {
    onComplete: (selector, doc) => {
        const selected = selector.selected;
        if (selected instanceof RenderVertex && selected.relIndex >= 0) {
            console.log('Selected existing point');
        } else {
            console.log('Created new point');
        }
    }
});
```

### Filtered Selection

Selection with custom filters:

```typescript
const filteredSelector = vertex({
    realEl: true,
    refinedFilter: (el: GeoRenderElement) => {
        // Only select points that are not on the origin
        const vertex = el as RenderVertex;
        return vertex.x !== 0 || vertex.y !== 0;
    }
});
```

### Sticky Selection

Maintain selection across interactions:

```typescript
const stickyLineSelector = stroke({
    selectableStrokeTypes: ['RenderLine'],
    stickyOnMatch: true,
    stickyPTypes: ['mouse', 'pen'] // Stick on mouse and pen input
});
```

## Selector DSL Examples

### Triangle Construction

```typescript
export class CreateTriangleTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertex({ preview: true }), {
        minCount: 3,
        maxCount: 3,
        onComplete: (selector, doc) => {
            this.createTriangle(selector.selected);
        }
    });
    
    onPointerDown(event: GeoPointerEvent): void {
        this.selector.trySelect(event, this.focusDocCtrl);
    }
}
```

### Intersection Point Tool

```typescript
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    private selector = repeat(stroke({
        selectableStrokeTypes: ['RenderLine', 'RenderCircle', 'RenderEllipse']
    }), {
        minCount: 2,
        maxCount: 2,
        onComplete: (selector, doc) => {
            this.createIntersectionPoints(selector.selected);
        }
    });
    
    onPointerDown(event: GeoPointerEvent): void {
        this.selector.trySelect(event, this.focusDocCtrl);
    }
}
```

### Angle by Three Points

```typescript
export class CreateAngleByThreePointsTool extends GeometryTool<CommonToolState> {
    private selector = then([
        vertex({ preview: true }), // Vertex of angle
        vertex({ preview: true }), // First ray point
        vertex({ preview: true })  // Second ray point
    ], {
        onComplete: (selector, doc) => {
            this.createAngle(selector.selected);
        }
    });
    
    onPointerDown(event: GeoPointerEvent): void {
        this.selector.trySelect(event, this.focusDocCtrl);
    }
}
```

## Selector State Management

### Selection Lifecycle

1. **Initialization**: Selector is created with options
2. **Hit Testing**: `tryHit()` checks for element matches
3. **Preview**: `tryPreview()` generates preview elements
4. **Acceptance**: Element is accepted and marked
5. **Completion**: `onComplete` callback is triggered
6. **Reset**: Selector is reset for next interaction

### State Properties

```typescript
interface SelectorState {
    selected?: SelectableType;      // Currently selected element
    acceptMarked: boolean;          // Whether selection is accepted
    markedSelect?: SelectableType;  // Marked selection
    isAccepted: boolean;           // Acceptance status
    isPartial: boolean;            // Partial selection (multi-selectors)
}
```

## Performance Optimizations

### Caching

Selectors use caching for performance:

```typescript
export class VertexSelector extends ElementSelector<RenderVertex> {
    private curPreviewEl?: RenderVertex;
    private lastHitEl?: RenderVertex;
    private curDoc?: GeoDocCtrl;
    
    // Cache hit results to avoid redundant calculations
    tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): RenderVertex | undefined {
        if (this.lastHitEl && this.curDoc === doc) {
            // Use cached result if conditions match
            return this.lastHitEl;
        }
        // Perform hit testing...
    }
}
```

### Efficient Hit Testing

Hit testing is optimized for performance:

```typescript
// Use spatial indexing for large numbers of elements
const candidates = doc.renderer.originObjects.potentialSelectionElements();
const hits = candidates
    .filter(el => this.options.refinedFilter?.(el) ?? true)
    .map(el => checkHitOnElement(event.position, el))
    .filter(hit => hit !== undefined);
```

## Best Practices

### 1. Use Appropriate Selector Types

```typescript
// For single element selection
const pointSelector = vertex({ preview: true });

// For multiple element selection
const multiPointSelector = repeat(vertex({ preview: true }), { minCount: 2 });

// For alternative selections
const pointOrLineSelector = or([vertex(), stroke()]);
```

### 2. Handle Selection States

```typescript
onPointerDown(event: GeoPointerEvent): void {
    const selected = this.selector.trySelect(event, this.focusDocCtrl);
    
    if (this.selector.isAccepted) {
        // Selection is complete
        this.processSelection(selected);
        this.selector.reset(); // Reset for next interaction
    } else if (this.selector.isPartial) {
        // Partial selection (waiting for more elements)
        this.updatePreview(selected);
    }
}
```

### 3. Provide Visual Feedback

```typescript
const selector = vertex({
    preview: true,
    cursor: new BehaviorSubject([pointerCursor]),
    onComplete: (selector, doc) => {
        // Provide completion feedback
        this.showCompletionIndicator();
    }
});
```

### 4. Use Filters Wisely

```typescript
const filteredSelector = stroke({
    selectableStrokeTypes: ['RenderLine'],
    refinedFilter: (el: GeoRenderElement) => {
        // Only select lines that are not construction lines
        return !el.renderProp?.isConstruction;
    }
});
```

## Common Patterns

### Point-to-Point Construction

```typescript
const pointToPointSelector = repeat(vertex({ preview: true }), {
    minCount: 2,
    maxCount: 2
});
```

### Point-on-Curve Selection

```typescript
const pointOnCurveSelector = or([
    vertex({ realEl: true }),
    vertexOnStroke({ 
        selectableStrokeTypes: ['RenderLine', 'RenderCircle'],
        showPosHint: true 
    })
]);
```

### Multi-Step Construction

```typescript
const multiStepSelector = then([
    stroke({ selectableStrokeTypes: ['RenderLine'] }),
    vertex({ preview: true }),
    vertex({ preview: true })
]);
```
