# Renderer System Documentation

## Overview

The renderer system provides high-performance 2D canvas rendering for geometric objects. It handles coordinate transformations, viewport management, object rendering, and visual feedback for the geometry editor.

## Architecture

### Renderer Components

```
renderer/
├── geo.renderer.ts         # Base renderer class
├── geo2d.renderer.ts      # 2D canvas renderer implementation
└── geo2d.renderer.utils.ts # Rendering utilities and helpers
```

## Base Renderer

### GeoRenderer Class

Abstract base class for all geometric renderers:

```typescript
export abstract class GeoRenderer {
    // Core properties
    public readonly docCtrl: GeoDocCtrl;
    public readonly layer: GraphicLayerCtrl;
    protected readonly viewport: BoardViewportManager;
    protected readonly canvas: HTMLCanvasElement;
    
    // Object collections
    protected originObjects: GeoObjCollection;
    protected previewObjects: GeoObjCollection;
    protected potentialSelection: GeoRenderElement[] = [];
    
    // Coordinate transformation
    protected currLookAt: Position;
    protected zoomUnit: number;
    
    constructor(docCtrl: GeoDocCtrl, layer: GraphicLayerCtrl) {
        this.docCtrl = docCtrl;
        this.layer = layer;
        this.viewport = docCtrl.viewport as BoardViewportManager;
        this.canvas = layer.canvas;
        this.clearState();
    }
    
    abstract render(): void;
    abstract clearBoard(): void;
}
```

### Object Management

```typescript
// Element management
addActualElement(element: GeoRenderElement): void {
    this.originObjects.addRenderElement(element);
}

addPreviewElement(element: GeoRenderElement): void {
    this.previewObjects.addRenderElement(element);
}

removePreviewByIds(previewEls: (GeoRenderElement | number)[]): void {
    const ids = previewEls.map(preview => 
        typeof preview === 'number' ? preview : preview.relIndex
    );
    this.previewObjects.removeByIds(ids);
    this.onElementChange.emit(new DefaultEventData('elementChange', previewEls));
}

// Element access
elementAt<T extends GeoRenderElement>(relIndex: number): T | undefined {
    return relIndex >= 0 ? 
        this.originObjects.elementAt(relIndex) : 
        this.previewObjects.elementAt(relIndex);
}

realElAt<T extends GeoRenderElement>(relIndex: number): T {
    return this.originObjects.elementAt(relIndex) as T;
}

previewElAt<T extends GeoRenderElement>(relIndex: number): T | undefined {
    return this.previewObjects.elementAt(relIndex) as T;
}
```

### Coordinate Transformations

```typescript
// Geometric to layer coordinate conversion
geoToLayerX(x: number): number {
    return (x + this.currLookAt.x) * this.zoomUnit;
}

geoToLayerY(y: number): number {
    return (y + this.currLookAt.y) * this.zoomUnit;
}

// Layer to geometric coordinate conversion
layerToGeoX(x: number): number {
    return x / this.zoomUnit - this.currLookAt.x;
}

layerToGeoY(y: number): number {
    return y / this.zoomUnit - this.currLookAt.y;
}

// Viewport management
lookAt(xInGeoCoord: number, yInGeoCoord: number): void {
    const docRenderProp = this.docCtrl.state.docRenderProp;
    docRenderProp.translation[0] = xInGeoCoord;
    docRenderProp.translation[1] = yInGeoCoord;
    
    if (this.zoomingToLevel && this.zoomingToLevel != docRenderProp.scale) {
        docRenderProp.scale = this.zoomingToLevel;
        delete this.zoomingToLevel;
    }
    
    this.docCtrl.rendererCtrl.render();
}
```

## 2D Canvas Renderer

### Geo2dRenderer Class

Concrete implementation for 2D canvas rendering:

```typescript
export class Geo2dRenderer extends GeoRenderer {
    declare context: CanvasRenderingContext2D;
    
    private highlightRefEls: Map<'Vertex' | 'Line' | 'Arc', Map<number, GeoRenderElement>>;
    private pendingRenderlabels: PendingRenderLabel[] = [];
    private renderedlabels: RenderedLabel[] = [];
    private checkOverlapStateList: GeoRenderElement[] = [];
    
    constructor(layer: GraphicLayerCtrl, doc: GeoDocCtrl) {
        super(doc, layer);
        this.context = layer.canvas.getContext('2d');
        layer.attachRenderer((vpm: ViewportManager, lc: GraphicLayerCtrl) => this.render());
    }
}
```

### Rendering Pipeline

```typescript
override render(): void {
    this.clearBoard();
    
    // Reset rendering state
    this.checkOverlapStateList = [];
    this.renderedlabels = [];
    this.pendingRenderlabels = [];
    
    // Render grid and axes
    this.renderGrid();
    
    // Render geometric objects
    this.renderObjects(false); // Real objects
    this.renderObjects(true);  // Highlighted objects
    
    // Render labels
    this.renderLabels();
    
    // Render potential selections
    this.renderPotentialSelections();
}

private renderObjects(highlight: boolean): void {
    const state = highlight ? this.originObjects : this.originObjects;
    
    // Get objects by type, sorted by rendering priority
    const shapes = state.polygons(highlight);
    const lines = state.lines(highlight);
    const circles = state.circles(highlight);
    const vertices = state.vertices(highlight);
    const angles = state.angles(highlight);
    
    // Render in order: shapes, lines, circles, vertices, angles
    for (const obj of shapes) this.renderObject(obj, highlight);
    for (const obj of lines) this.renderObject(obj, highlight);
    for (const obj of circles) this.renderObject(obj, highlight);
    for (const obj of vertices) this.renderObject(obj, highlight);
    for (const obj of angles) this.renderObject(obj, highlight);
}
```

### Object Rendering

```typescript
private renderObject(rel: GeoRenderElement, highlight: boolean = false): void {
    switch (rel.type) {
        case 'RenderVertex':
            this.renderVertex(rel as RenderVertex, highlight);
            break;
        case 'RenderLine':
        case 'RenderLineSegment':
        case 'RenderVector':
        case 'RenderRay':
            this.renderLine(rel as RenderLine, highlight);
            break;
        case 'RenderCircle':
            this.renderCircle(rel as RenderCircle, highlight);
            break;
        case 'RenderCircleShape':
            this.renderCircleShape(rel as RenderCircleShape, highlight);
            break;
        case 'RenderPolygon':
            this.renderPolygon(rel as RenderPolygon, highlight);
            break;
        case 'RenderAngle':
            this.renderAngle(rel as RenderAngle, highlight);
            break;
        case 'RenderEllipse':
            this.renderEllipse(rel as RenderEllipse, highlight);
            break;
        case 'RenderSector':
            this.renderSector(rel as RenderSector, highlight);
            break;
        default:
            break;
    }
}
```

### Specific Rendering Methods

#### Vertex Rendering

```typescript
private renderVertex(vertex: RenderVertex, highlight: boolean): void {
    const x = this.geoToLayerX(vertex.x);
    const y = this.geoToLayerY(vertex.y);
    const renderProp = vertex.renderProp || this.docCtrl.docDefaultElRenderProps.vertex;
    
    const radius = highlight ? 
        RENDER_VERTEX_RADIUS_POTENTIAL : 
        (renderProp.radius || RENDER_VERTEX_RADIUS);
    
    const strokeColor = highlight ? 
        'orange' : 
        (renderProp.strokeColor || 'black');
    
    const fillColor = renderProp.fillColor || 'white';
    
    this.context.save();
    this.context.beginPath();
    this.context.arc(x, y, radius, 0, 2 * Math.PI);
    
    // Fill
    if (fillColor) {
        this.context.fillStyle = fillColor;
        this.context.fill();
    }
    
    // Stroke
    this.context.strokeStyle = strokeColor;
    this.context.lineWidth = renderProp.lineWeight || 1;
    this.context.stroke();
    
    this.context.restore();
    
    // Add label if needed
    if (vertex.name) {
        this.addPendingLabel(vertex.name, new DOMPoint(x, y), vertex, highlight);
    }
}
```

#### Line Rendering

```typescript
private renderLine(line: RenderLine, highlight: boolean): void {
    const renderProp = line.renderProp || this.docCtrl.docDefaultElRenderProps.line;
    const strokeColor = highlight ? 'orange' : (renderProp.strokeColor || 'black');
    const lineWeight = renderProp.lineWeight || 1;
    
    this.context.save();
    this.context.strokeStyle = strokeColor;
    this.context.lineWidth = lineWeight;
    
    if (renderProp.strokeStyle?.lineDash) {
        this.context.setLineDash(renderProp.strokeStyle.lineDash);
    }
    
    this.context.beginPath();
    
    switch (line.type) {
        case 'RenderLine':
            this.renderInfiniteLine(line);
            break;
        case 'RenderLineSegment':
            this.renderLineSegment(line as RenderLineSegment);
            break;
        case 'RenderRay':
            this.renderRay(line as RenderRay);
            break;
        case 'RenderVector':
            this.renderVector(line as RenderVector);
            break;
    }
    
    this.context.stroke();
    this.context.restore();
}

private renderLineSegment(segment: RenderLineSegment): void {
    const startPoint = segment.startPoint(this);
    const endPoint = segment.endPoint(this);
    
    const x1 = this.geoToLayerX(startPoint.x);
    const y1 = this.geoToLayerY(startPoint.y);
    const x2 = this.geoToLayerX(endPoint.x);
    const y2 = this.geoToLayerY(endPoint.y);
    
    this.context.moveTo(x1, y1);
    this.context.lineTo(x2, y2);
}
```

#### Circle Rendering

```typescript
private renderCircle(circle: RenderCircle, highlight: boolean): void {
    const centerX = this.geoToLayerX(circle.centerX);
    const centerY = this.geoToLayerY(circle.centerY);
    const radius = circle.radius * this.zoomUnit;
    
    const renderProp = circle.renderProp || this.docCtrl.docDefaultElRenderProps.circle;
    const strokeColor = highlight ? 'orange' : (renderProp.strokeColor || 'black');
    
    this.context.save();
    this.context.beginPath();
    this.context.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    
    this.context.strokeStyle = strokeColor;
    this.context.lineWidth = renderProp.lineWeight || 1;
    this.context.stroke();
    
    this.context.restore();
}

private renderCircleShape(circleShape: RenderCircleShape, highlight: boolean): void {
    // Render circle outline
    this.renderCircle(circleShape, highlight);
    
    // Fill if specified
    const renderProp = circleShape.renderProp;
    if (renderProp?.fillColor) {
        const centerX = this.geoToLayerX(circleShape.centerX);
        const centerY = this.geoToLayerY(circleShape.centerY);
        const radius = circleShape.radius * this.zoomUnit;
        
        this.context.save();
        this.context.beginPath();
        this.context.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        this.context.fillStyle = renderProp.fillColor;
        this.context.fill();
        this.context.restore();
    }
}
```

### Grid Rendering

```typescript
private renderGrid(): void {
    if (!this.viewport.currentLookAt) return;
    
    const scale = this.getScale();
    const spacing = calculateSpacing(this.docCtrl, this.viewport);
    
    const leftGeo = this.layerToGeoX(this.left);
    const rightGeo = this.layerToGeoX(this.right);
    const topGeo = this.layerToGeoY(this.top);
    const bottomGeo = this.layerToGeoY(this.bottom);
    
    // Render grid lines
    this.renderGridLines(leftGeo, rightGeo, topGeo, bottomGeo, spacing);
    
    // Render axes
    this.renderAxes(leftGeo, rightGeo, topGeo, bottomGeo);
    
    // Render grid labels
    this.renderGridLabels(leftGeo, rightGeo, topGeo, bottomGeo, spacing);
}

private renderGridLines(left: number, right: number, top: number, bottom: number, spacing: number): void {
    this.context.save();
    this.context.strokeStyle = LineColor.Grid;
    this.context.lineWidth = LineWeight.Grid;
    
    // Vertical lines
    const startX = Math.floor(left / spacing) * spacing;
    for (let x = startX; x <= right; x += spacing) {
        const layerX = this.geoToLayerX(x);
        this.context.beginPath();
        this.context.moveTo(layerX, this.geoToLayerY(top));
        this.context.lineTo(layerX, this.geoToLayerY(bottom));
        this.context.stroke();
    }
    
    // Horizontal lines
    const startY = Math.floor(bottom / spacing) * spacing;
    for (let y = startY; y <= top; y += spacing) {
        const layerY = this.geoToLayerY(y);
        this.context.beginPath();
        this.context.moveTo(this.geoToLayerX(left), layerY);
        this.context.lineTo(this.geoToLayerX(right), layerY);
        this.context.stroke();
    }
    
    this.context.restore();
}
```

### Label Rendering

```typescript
private addPendingLabel(text: string, position: DOMPoint, object: GeoRenderElement, highlight?: boolean): void {
    this.pendingRenderlabels.push({
        text: text,
        vertexPoint: position,
        object: object,
        highlight: highlight
    });
}

private renderLabels(): void {
    for (const label of this.pendingRenderlabels) {
        const fontSize = 12;
        const metrics = getLabelMeasure(this.context, label.text, fontSize);
        
        // Calculate label position to avoid overlaps
        const position = this.calculateLabelPosition(
            label.vertexPoint, 
            metrics.width, 
            fontSize,
            label.object
        );
        
        // Render label
        drawLabel(this.context, label.text, position, {
            fontSize: fontSize,
            textColor: label.highlight ? 'orange' : 'black'
        });
        
        // Track rendered label for overlap detection
        this.renderedlabels.push({
            bottomLeftPoint: position,
            width: metrics.width,
            height: fontSize
        });
    }
}
```

## Rendering Utilities

### Drawing Functions

```typescript
// Line drawing
export function drawLine(
    ctx: CanvasRenderingContext2D,
    x1: number, y1: number, x2: number, y2: number,
    options: {
        color?: string;
        lineWeight?: number;
        lineDash?: number[];
        withoutSave?: boolean;
    }
): void {
    const color = options?.color ?? 'black';
    const lineWeight = options?.lineWeight ?? 1;
    const withoutSave = options?.withoutSave ?? false;
    
    if (!withoutSave) ctx.save();
    ctx.beginPath();
    
    if (options?.lineDash?.length) ctx.setLineDash(options.lineDash);
    ctx.lineWidth = lineWeight;
    ctx.strokeStyle = color;
    
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();
    
    ctx.closePath();
    if (!withoutSave) ctx.restore();
}

// Arc drawing
export function drawArc(
    ctx: CanvasRenderingContext2D,
    centerX: number, centerY: number, radius: number,
    startAngle: number, endAngle: number,
    options: {
        color?: string;
        lineWeight?: number;
        fillColor?: string;
    }
): void {
    ctx.save();
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
    
    if (options.fillColor) {
        ctx.fillStyle = options.fillColor;
        ctx.fill();
    }
    
    if (options.color) {
        ctx.strokeStyle = options.color;
        ctx.lineWidth = options.lineWeight || 1;
        ctx.stroke();
    }
    
    ctx.restore();
}
```

### Label Utilities

```typescript
export function getLabelMeasure(
    ctx: CanvasRenderingContext2D, 
    content: string, 
    fontSize: number
): TextMetrics {
    ctx.save();
    ctx.font = fontSize + 'px Montserrat';
    const metrics = ctx.measureText(content);
    ctx.restore();
    return metrics;
}

export function drawLabel(
    ctx: CanvasRenderingContext2D,
    content: string,
    bottomLeft: Position,
    options?: {
        fontSize?: number;
        textColor?: string;
        radAngleFromXAxis?: number;
        flipVertical?: boolean;
        flipHorizontal?: boolean;
        padding?: { top?: number; left?: number; };
    }
): void {
    const fontSize = options?.fontSize ?? 12;
    const textColor = options?.textColor ?? 'black';
    const padding = options?.padding ?? { top: 2, left: 2 };
    
    ctx.save();
    ctx.font = fontSize + 'px Montserrat';
    ctx.fillStyle = textColor;
    ctx.textBaseline = 'bottom';
    
    const x = bottomLeft.x + (padding.left || 0);
    const y = bottomLeft.y - (padding.top || 0);
    
    if (options?.radAngleFromXAxis) {
        ctx.translate(x, y);
        ctx.rotate(options.radAngleFromXAxis);
        ctx.fillText(content, 0, 0);
    } else {
        ctx.fillText(content, x, y);
    }
    
    ctx.restore();
}
```

## Performance Optimization

### Viewport Culling

```typescript
private isElementVisible(element: GeoRenderElement): boolean {
    const bounds = this.getElementBounds(element);
    const viewportBounds = this.getViewportBounds();
    return bounds.intersects(viewportBounds);
}

private renderVisibleElements(): void {
    const visibleElements = this.originObjects.allElements()
        .filter(el => this.isElementVisible(el));
    
    for (const element of visibleElements) {
        this.renderObject(element);
    }
}
```

### Render Scheduling

```typescript
scheduleRender(): void {
    if (this.requestedRenderFrame) return;
    
    requestAnimationFrame(() => {
        this.requestedRenderFrame = false;
        this.layer.renderer(this.layer.viewport, this.layer);
    });
    
    this.requestedRenderFrame = true;
}
```

### Canvas Optimization

```typescript
private useCanvasCoord(): void {
    this.context.setTransform(1, 0, 0, 1, 0, 0);
}

override clearBoard(): void {
    this.context.save();
    this.useCanvasCoord();
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.context.restore();
}
```

## Best Practices

1. **Rendering Order**: Render shapes before lines, lines before points
2. **State Management**: Always save/restore canvas state
3. **Performance**: Use viewport culling for large numbers of objects
4. **Coordinate Systems**: Consistently use coordinate transformation methods
5. **Visual Feedback**: Provide clear visual feedback for interactions
6. **Label Management**: Avoid label overlaps and provide readable text
7. **Memory Management**: Clean up resources when renderer is removed
