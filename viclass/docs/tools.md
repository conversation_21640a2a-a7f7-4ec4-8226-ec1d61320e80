# Tài Liệu Hệ Thống Công Cụ

## Tổng Quan

Hệ thống công cụ cung cấp một bộ công cụ toàn diện để tạo và thao tác hình học. Mỗi công cụ tuân theo một mẫu nhất quán và tích hợp với hệ thống selector để tương tác với phần tử.

## Kiến Trúc Công Cụ

### Lớp Công Cụ Cơ Sở

Tất cả các công cụ hình học đều mở rộng lớp cơ sở `GeometryTool`:

```typescript
export abstract class GeometryTool<T extends ToolState> extends Tool<T, GeometryToolBar> {
    protected focusDocCtrl: GeoDocCtrl;
    protected editor: GeometryEditor;

    // Bộ xử lý sự kiện
    abstract onPointerDown(event: GeoPointerEvent): void;
    onPointerMove?(event: GeoPointerEvent): void;
    onPointerUp?(event: GeoPointerEvent): void;
    onKeyDown?(event: GeoKeyboardEvent): void;
}
```

### Quản Lý Trạng Thái Công Cụ

Mỗi công cụ duy trì trạng thái của nó thông qua một đối tượng `ToolState`:

```typescript
export class CommonToolState implements ToolState {}

export class TriangleToolState extends CommonToolState {
    drawMode: number = 0; // 0: bằng 3 điểm, 1: bằng đáy và chiều cao
}

export class QuadToolState extends CommonToolState {
    drawMode: number = 0; // Các chế độ khác nhau cho các tứ giác khác nhau
}
```

## Công Cụ Điểm

### CreatePointTool

Tạo điểm tại các vị trí được chỉ định:

```typescript
export class CreatePointTool extends GeometryTool<CommonToolState> {
    private selector = vertex({ preview: true, autoAccept: true });

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (selected) {
            this.createPoint(selected);
        }
    }
}
```

### MiddlePointTool

Tạo điểm giữa giữa hai điểm:

```typescript
export class MiddlePointTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertex({ preview: true }), {
        minCount: 2,
        maxCount: 2,
    });

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createMiddlePoint(selected);
        }
    }
}
```

### IntersectionPointTool

Tạo điểm giao giữa các đối tượng hình học:

```typescript
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    private selector = repeat(
        stroke({
            selectableStrokeTypes: ['RenderLine', 'RenderCircle', 'RenderEllipse'],
        }),
        { minCount: 2, maxCount: 2 }
    );

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createIntersectionPoints(selected);
        }
    }
}
```

## Công Cụ Đường

### CreateLineTool

Tạo đường thẳng vô hạn qua hai điểm:

```typescript
export class CreateLineTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createLine(selected);
        }
    }
}
```

### CreateLineSegmentTool

Tạo đoạn thẳng giữa hai điểm:

```typescript
export class CreateLineSegmentTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createLineSegment(selected);
        }
    }
}
```

### CreateParallelLineTool

Tạo đường thẳng song song với đường thẳng hiện có:

```typescript
export class CreateParallelLineTool extends GeometryTool<CommonToolState> {
    private selector = then([stroke({ selectableStrokeTypes: ['RenderLine'] }), vertex({ preview: true })]);

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createParallelLine(selected);
        }
    }
}
```

## Công Cụ Hình

### CreateCircleTool

Tạo đường tròn với tâm và bán kính:

```typescript
export class CreateCircleTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createCircle(selected);
        }
    }
}
```

### CreateTriangleTool

Tạo tam giác với các chế độ khác nhau:

```typescript
export class CreateTriangleTool extends GeometryTool<TriangleToolState> {
    private selector = repeat(vertexS(), { minCount: 3, maxCount: 3 });

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createTriangle(selected);
        }
    }
}
```

### CreateSquareTool

Tạo hình vuông với logic xây dựng đặc biệt:

```typescript
export class CreateSquareTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertexS(), { minCount: 2, maxCount: 2 });

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (this.selector.isAccepted) {
            this.createSquare(selected);
        }
    }

    private createSquare(vertices: SelectedVertex[]): void {
        const [v1, v2] = vertices;
        // Tính toán các đỉnh hình vuông sử dụng thuật toán hướng
        const direction = nthDirectionByLine([v2.x - v1.x, v2.y - v1.y], [v1.x, v1.y], [v2.x, v2.y]);
        // Tạo xây dựng hình vuông...
    }
}
```

## Công Cụ Biến Đổi

### MoveElementTool

Di chuyển các phần tử hình học:

```typescript
export class MoveElementTool extends GeometryTool<CommonToolState> {
    private selector = or([
        vertex({ realEl: true }),
        stroke({ selectableStrokeTypes: ['RenderLine', 'RenderCircle'] }),
    ]);

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (selected) {
            this.startMove(selected, event);
        }
    }
}
```

## Mẫu Xây Dựng Công Cụ

### Yêu Cầu Xây Dựng

Các công cụ tạo đối tượng hình học thông qua yêu cầu xây dựng:

```typescript
interface GeoElConstructionRequest {
    ctId: string;
    elType: GeoObjectType;
    cgName: string;
    paramSpecs?: ParamSpecs[];
}

// Ví dụ: Xây dựng điểm
const pointConstruction: GeoElConstructionRequest = {
    ctId: 'Point',
    elType: 'Point',
    cgName: 'PointByCoordinates',
    paramSpecs: [
        { paramKind: 'value', value: x },
        { paramKind: 'value', value: y },
    ],
};
```

### Xây Dựng Từ Xa

Các công cụ sử dụng xây dựng từ xa để xác thực phía máy chủ:

```typescript
async function remoteConstruct(
    editor: GeometryEditor,
    constructions: ConstructionRequest[],
    docCtrl: GeoDocCtrl
): Promise<ApplyConstructionResponse> {
    return await editor.geoGateway.construct(docCtrl.state.globalId, constructions);
}
```

## Tích Hợp Công Cụ

### Tích Hợp Selector

Các công cụ tích hợp với hệ thống selector:

```typescript
export class ExampleTool extends GeometryTool<CommonToolState> {
    // Định nghĩa selector cho các chế độ tương tác khác nhau
    private pointSelector = vertex({ preview: true });
    private lineSelector = stroke({ selectableStrokeTypes: ['RenderLine'] });
    private combinedSelector = or([this.pointSelector, this.lineSelector]);

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.combinedSelector.trySelect(event, this.focusDocCtrl);
        if (selected) {
            this.handleSelection(selected);
        }
    }
}
```

## Vòng Đời Công Cụ

1. **Khởi Tạo**: Công cụ được tạo và thêm vào thanh công cụ
2. **Kích Hoạt**: Công cụ trở nên hoạt động khi được chọn
3. **Tương Tác**: Công cụ xử lý các sự kiện con trỏ và bàn phím
4. **Xây Dựng**: Công cụ tạo các đối tượng hình học
5. **Hủy Kích Hoạt**: Công cụ bị hủy kích hoạt khi công cụ khác được chọn

## Phân Tích Chi Tiết: CreatePolygonTool

### Tổng Quan

`CreatePolygonTool` là một công cụ phức tạp cho phép người dùng tạo đa giác bằng cách click liên tiếp các điểm. Công cụ này có khả năng tự động hoàn thành đa giác khi người dùng click vào điểm đầu tiên.

### Kiến Trúc Lớp

```typescript
export class CreatePolygonTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreatePolygonTool';

    declare selLogic: RepeatSelector<SelectedVertex>;
    pQ = new PreviewQueue();
    firstPoint?: RenderVertex;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }
}
```

### Thuộc Tính Chính

- **`selLogic`**: RepeatSelector để xử lý lựa chọn nhiều điểm
- **`pQ`**: PreviewQueue để quản lý các phần tử preview
- **`firstPoint`**: RenderVertex đặc biệt để đánh dấu điểm đầu tiên (màu đỏ)

### Hệ Thống Selector

#### Tạo Logic Selector

```typescript
createSelLogic() {
    this.selLogic = nPoints(this.pQ, this.pointerHandler.cursor, {
        onPartialSelection: this.newPointSelected.bind(this),
    });
    this.selLogic.setOption('onComplete', this.performConstruction.bind(this));
}
```

Sử dụng `nPoints` selector với:

- **Không giới hạn số điểm**: Cho phép tạo đa giác với số đỉnh tùy ý
- **onPartialSelection**: Callback được gọi mỗi khi có điểm mới được chọn
- **onComplete**: Callback được gọi khi hoàn thành đa giác

#### Logic Lựa Chọn Điểm Mới

```typescript
newPointSelected(newSel: SelectedVertex, curSel: SelectedVertex[], selector: any, doc: GeoDocCtrl): boolean {
    if (curSel.length >= 3)
        this.selLogic.get('vertex').setOption('preview', true); // Cho phép chọn preview point

    let complete = false;
    if (curSel.length > 2 && vert(newSel).relIndex == this.firstPoint?.relIndex) {
        // Nếu điểm mới trùng với điểm đầu tiên, hoàn thành đa giác
        complete = true;
    }
    return !complete; // Trả về false để dừng lựa chọn
}
```

**Logic hoạt động:**

1. Sau khi có ≥3 điểm, cho phép chọn preview elements
2. Kiểm tra nếu điểm mới trùng với điểm đầu tiên
3. Nếu trùng và đã có >2 điểm, hoàn thành đa giác

### Hệ Thống Preview

#### Tạo Preview Elements

```typescript
private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
    const selected = this.selLogic.trySelect(event, ctrl);

    if (selected && selected.length > 1) {
        // Tạo điểm đầu tiên màu đỏ để highlight
        const v = vert(selected[0]);
        this.firstPoint = pVertex(-21, v.coords);
        this.firstPoint.renderProp = buildPreviewVertexRenderProp();
        this.firstPoint.renderProp.pointColor = '#ff0000';
        this.pQ.add(this.firstPoint);

        // Tạo preview polygon
        const p = pPolygon(
            ctrl,
            -20,
            selected.map(s => vert(s).coords),
            true,
            RenderVector
        );
        this.pQ.add(p);
    }

    this.pQ.flush(ctrl);
}
```

**Các preview elements:**

1. **First Point Highlight**: Điểm đầu tiên được tô màu đỏ (#ff0000)
2. **Preview Polygon**: Đa giác preview với các cạnh được vẽ

#### Quản lý Preview Queue

```typescript
export class PreviewQueue {
    private queue: GeoRenderElement[] = [];

    add(...elements: GeoRenderElement[]): void {
        elements.forEach(element => {
            if (element && !this.queue.find(el => el.relIndex === element.relIndex)) {
                this.queue.push(element);
            }
        });
    }

    async flush(docCtrl: GeoDocCtrl): Promise<void> {
        // Đồng bộ hóa tất cả preview elements với renderer
    }
}
```

### Xây Dựng Đa Giác

#### Xử Lý Construction

```typescript
protected async performConstruction(selector: RepeatSelector<SelectedVertex>, ctrl: GeoDocCtrl) {
    // Loại bỏ điểm cuối (trùng với điểm đầu)
    selector.selected.splice(selector.selected.length - 1, 1);

    // Gán tên cho các điểm và đa giác
    const { pcs, points, strokes } = await assignNames(
        ctrl,
        selector.selected,
        this.toolbar.getTool('NamingElementTool') as NamingElementTool,
        'Đa giác'
    );

    if (!pcs) {
        this.resetState();
        return;
    }

    // Tạo tên đa giác từ tên các điểm
    const polygonName = points.map(p => p.name).join('');
    const constructionPolygon = this.buildPolygonConstruction(polygonName);

    try {
        await remoteConstruct(ctrl, constructionPolygon, pcs, this.editor.geoGateway, 'Đa giác');
    } finally {
        this.resetState();
    }
}
```

#### Xây Dựng Construction Request

```typescript
private buildPolygonConstruction(name: string): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest('Polygon/PolygonEC', 'Polygon', 'ByPointsName');
    construction.name = name;
    construction.paramSpecs = [];
    return construction;
}
```

### Quản Lý Trạng Thái

#### Reset State

```typescript
override resetState() {
    this.selLogic.reset();
    this.selLogic.get('vertex').setOption('preview', false);
    super.resetState();
}
```

**Các bước reset:**

1. Reset selector logic
2. Tắt chế độ preview selection
3. Gọi reset của lớp cha

### Xử Lý Sự Kiện

#### Pointer Event Handling

```typescript
override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
    if (event.eventType == 'pointerdown') {
        if (!this.shouldHandleClick(event)) return event;
    }

    const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
    if (!ctrl?.state) throw new GeoPointerNotInError();

    if (event.eventType == 'pointermove')
        this.pointerMoveCachingReflowSync.handleEvent(event, event =>
            handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
        );
    else this.doTrySelection(event, ctrl);

    event.continue = false;
    event.nativeEvent.preventDefault();
    return event;
}
```

### Tính Năng Đặc Biệt

#### 1. Auto-Complete Polygon

- Tự động hoàn thành khi click vào điểm đầu tiên
- Yêu cầu tối thiểu 3 điểm để tạo đa giác hợp lệ

#### 2. Visual Feedback

- Điểm đầu tiên được highlight màu đỏ
- Preview polygon được vẽ real-time
- Cursor thay đổi theo trạng thái

#### 3. Naming System

- Tự động gán tên cho các điểm chưa có tên
- Tên đa giác được tạo từ tên các điểm (VD: "ABCD")
- Tích hợp với NamingElementTool

### Ví Dụ Sử Dụng

```typescript
// Tạo polygon tool
const polygonTool = new CreatePolygonTool(editor, toolbar);

// Kích hoạt tool
toolbar.activateTool('CreatePolygonTool');

// Người dùng click các điểm:
// 1. Click điểm A -> tạo điểm đầu tiên
// 2. Click điểm B -> tạo preview line AB
// 3. Click điểm C -> tạo preview triangle ABC, highlight điểm A màu đỏ
// 4. Click điểm D -> tạo preview quadrilateral ABCD
// 5. Click lại điểm A -> hoàn thành polygon ABCD
```

### Tối Ưu Hóa Hiệu Suất

1. **Caching Preview**: Sử dụng PreviewQueue để tránh render dư thừa
2. **Event Throttling**: pointermove được cache để tránh lag
3. **Efficient Hit Testing**: Chỉ test hit trên các elements cần thiết
4. **Memory Management**: Reset state đúng cách để tránh memory leak

## Thực Hành Tốt Nhất

1. **Sử Dụng Selector**: Luôn sử dụng DSL selector để lựa chọn phần tử
2. **Quản Lý Trạng Thái**: Duy trì trạng thái công cụ đúng cách
3. **Phần Tử Preview**: Cung cấp phản hồi trực quan trong quá trình xây dựng
4. **Xử Lý Lỗi**: Xử lý các xây dựng không hợp lệ một cách nhẹ nhàng
5. **Hiệu Suất**: Tối ưu hóa cho tương tác mượt mà
6. **Nhất Quán**: Tuân theo các mẫu đã thiết lập cho các công cụ tương tự
