# Viclass Geometry Editor Documentation

## Overview

The Viclass Geometry Editor is a comprehensive 2D geometry construction and manipulation system built with TypeScript and Angular. It provides a powerful set of tools for creating, editing, and solving geometric problems with an intuitive interface and robust mathematical foundation.

## Architecture

The geometry editor follows a modular architecture with clear separation of concerns:

```
viclass/packages/editors/viclass/editor.geo/
├── src/lib/
│   ├── tools/           # Geometry creation and manipulation tools
│   ├── selectors/       # Element selection system with DSL
│   ├── nth.direction/   # Direction calculation algorithms
│   ├── model/          # Data models and structures
│   ├── solving/        # Mathematical solving algorithms
│   ├── objects/        # Document controllers and hit detection
│   ├── renderer/       # 2D rendering system
│   ├── cmd/            # Command system
│   ├── history/        # Undo/redo functionality
│   └── extension.function/ # Extension functions
```

## Core Components

### 1. Geometry Editor (`geo.editor.ts`)

The main editor class that orchestrates all components:

```typescript
export class GeometryEditor extends EditorBase<GeoDocCtrl> 
    implements DocumentEditor, SupportInternalSelectFeature {
    
    // Core features
    selectionFeature: SelectionFeature;
    panFeature: PanFeature;
    zoomFeature: ZoomFeature;
    awarenessFeature: AwarenessFeature;
    
    // Document management
    createDocument(initData: GeoDocInitData): Promise<GeoDocCtrl>;
    createToolbar(): ToolBar<any, any>;
}
```

### 2. Tools System

Comprehensive set of geometry tools for creating and manipulating objects:

- **Point Tools**: CreatePointTool, MiddlePointTool, IntersectionPointTool
- **Line Tools**: CreateLineTool, CreateLineSegmentTool, CreateRayTool, CreateVectorTool
- **Shape Tools**: CreateCircleTool, CreateEllipseTool, CreatePolygonTool
- **Triangle Tools**: CreateTriangleTool, CreateRightTriangleTool, CreateIsoscelesTriangleTool
- **Quadrilateral Tools**: CreateSquareTool, CreateRectangleTool, CreateParallelogramTool
- **Transformation Tools**: CreateParallelLineTool, CreatePerpendicularLineTool

### 3. Selector System

Advanced element selection with Domain Specific Language (DSL):

```typescript
// Example selector usage
const vertexSelector = vertex({ preview: true, autoAccept: true });
const strokeSelector = stroke({ selectableStrokeTypes: ['RenderLine'] });
const orSelector = or([vertexSelector, strokeSelector], { flatten: true });
```

### 4. Mathematical Solving

Robust mathematical foundation for geometric calculations:

- **Polynomial Solving**: Linear, Quadratic, Cubic, Quartic equations
- **Complex Numbers**: Full complex arithmetic with trigonometric functions
- **Coefficients**: Ellipse equation manipulation

### 5. Rendering System

High-performance 2D canvas rendering:

```typescript
export class Geo2dRenderer extends GeoRenderer {
    render(): void;
    renderObject(rel: GeoRenderElement, highlight?: boolean): void;
    renderGrid(): void;
}
```

## Key Features

### Geometric Object Types

The editor supports a wide range of geometric objects:

```typescript
export type GeoObjectType =
    | 'Point' | 'Line' | 'LineSegment' | 'Ray' | 'Vector'
    | 'Circle' | 'Ellipse' | 'Semicircle' | 'CircularSector'
    | 'Triangle' | 'RightTriangle' | 'IsoscelesTriangle' | 'EquilateralTriangle'
    | 'Polygon' | 'RegularPolygon' | 'Quadrilateral'
    | 'Rectangle' | 'Square' | 'Parallelogram' | 'Rhombus' | 'Trapezoid'
    | 'Angle';
```

### Tool Types

Comprehensive tool system:

```typescript
export type GeometryToolType =
    | 'CreatePointTool' | 'CreateLineTool' | 'CreateCircleTool'
    | 'CreateTriangleTool' | 'CreateSquareTool' | 'CreateAngleTool'
    | 'IntersectionPointTool' | 'MiddlePointTool' | 'PointOnObjectTool'
    | 'CreateParallelLineTool' | 'CreatePerpendicularLineTool'
    | 'MoveElementTool' | 'UpdatePropTool' | 'RenameElementTool'
    | 'GeoPanTool' | 'GeoZoomTool' | 'InputCommandTool';
```

### Selection DSL

Powerful selection language for complex interactions:

```typescript
// Sequential selection
const thenSelector = then([
    vertex({ preview: true }),
    stroke({ selectableStrokeTypes: ['RenderLine'] })
]);

// Repeated selection
const repeatSelector = repeat(vertex({ preview: true }), { 
    minCount: 3, 
    maxCount: 5 
});

// Conditional selection
const orSelector = or([
    vertex({ preview: true }),
    stroke({ selectableStrokeTypes: ['RenderLine', 'RenderCircle'] })
]);
```

## Getting Started

### Basic Usage

```typescript
// Create geometry editor
const editor = new GeometryEditor(config, coordinator);

// Create document
const docCtrl = await editor.createDocument({
    numDim: 2,
    kind: GeoKind.GEO2D,
    boundary: { x: 0, y: 0, width: 800, height: 600 },
    docRenderProp: defaultRenderProp
});

// Create toolbar and tools
const toolbar = editor.createToolbar();
const pointTool = toolbar.getTool('CreatePointTool');
```

### Tool Implementation Example

```typescript
export class CreatePointTool extends GeometryTool<CommonToolState> {
    private selector = vertex({ preview: true, autoAccept: true });
    
    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (selected) {
            this.createPoint(selected);
        }
    }
    
    private async createPoint(vertex: RenderVertex): Promise<void> {
        const construction = buildPointConstruction(vertex);
        await remoteConstruct(this.editor, [construction], this.focusDocCtrl);
    }
}
```

## Documentation Structure

- [Tools System](./tools.md) - Detailed tool documentation
- [Selector System](./selectors.md) - Selection DSL and patterns
- [Direction Algorithms](./nth-direction.md) - Geometric direction calculations
- [Data Models](./model.md) - Object models and structures
- [Mathematical Solving](./solving.md) - Polynomial and complex number solving
- [Document Objects](./objects.md) - Document controllers and hit detection
- [Rendering System](./renderer.md) - 2D canvas rendering
- [API Reference](./api-reference.md) - Complete API documentation

## Contributing

When extending the geometry editor:

1. Follow the existing architectural patterns
2. Use the selector DSL for consistent element selection
3. Implement proper tool state management
4. Add comprehensive tests for mathematical algorithms
5. Document new features and APIs

## Performance Considerations

- Object collections use efficient caching mechanisms
- Rendering is optimized with viewport culling
- Mathematical calculations use epsilon-based comparisons
- Preview elements are managed separately from real elements
