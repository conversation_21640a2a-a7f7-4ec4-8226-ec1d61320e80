# Tài Liệu Hệ Thống Selector

## Tổng Quan

Hệ thống selector cung cấp một Ngôn Ngữ Đặc Th<PERSON> (DSL) mạnh mẽ để mô tả logic lựa chọn phần tử phức tạp trong các công cụ hình học. Nó đảm bảo hành vi lựa chọn nhất quán trên các công cụ khác nhau và cho phép các mẫu tương tác tinh vi.

## Kiến Trúc

### Lớp Selector Cơ Sở

Tất cả các selector đều mở rộng lớp cơ sở `ElementSelector`:

```typescript
export abstract class ElementSelector<T extends SelectableType, OT extends SelectorOptions<T>> {
    abstract multiple: boolean;
    selected?: T;
    acceptMarked: boolean = false;
    
    abstract tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;
    abstract tryPreview(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;
    abstract clearTrial(doc: GeoDocCtrl): void;
}
```

### Tùy Chọn Selector

Các selector được cấu hình thông qua các tùy chọn:

```typescript
export interface SelectorOptions<T extends SelectableType> {
    preview?: boolean;           // Bật phần tử preview
    realEl?: boolean;           // Bật phần tử thực
    autoAccept?: boolean;       // Tự động chấp nhận khi lựa chọn
    stickyOnMatch?: boolean;    // Dính vào phần tử đã khớp
    cursor?: BehaviorSubject<Cursor[]>;
    refinedFilter?: (el: GeoRenderElement) => boolean;
    onComplete?: (selector: ElementSelector<T>, doc: GeoDocCtrl) => void;
    onReset?: (selector: ElementSelector<T>) => void;
}
```

## Selector Cơ Bản

### Vertex Selector

Lựa chọn các phần tử điểm:

```typescript
export function vertex(options?: VertexSelectorOptions): VertexSelector {
    return new VertexSelector(options);
}

// Ví dụ sử dụng
const pointSelector = vertex({ preview: true, autoAccept: true });
const realPointSelector = vertex({ realEl: true });
const stickyPointSelector = vertex({ stickyOnMatch: true });
```

### Stroke Selector

Lựa chọn các phần tử dựa trên đường:

```typescript
export function stroke(options?: StrokeSelectorOptions): StrokeSelector {
    return new StrokeSelector(options);
}

// Ví dụ sử dụng
const lineSelector = stroke({ 
    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment'] 
});
const circleSelector = stroke({ 
    selectableStrokeTypes: ['RenderCircle'] 
});
const anyStrokeSelector = stroke(); // Tất cả các loại stroke
```

### Vertex on Stroke Selector

Lựa chọn điểm trên các đường cong hình học:

```typescript
export function vertexOnStroke(options?: VertexOnStrokeSelectorOptions): VertexOnStrokeSelector {
    return new VertexOnStrokeSelector(options);
}

// Ví dụ sử dụng
const pointOnLineSelector = vertexOnStroke({
    selectableStrokeTypes: ['RenderLine'],
    tfunc: (stroke, vertex, doc) => {
        // Biến đổi vị trí đỉnh trên stroke
        return transformedVertex;
    },
    showPosHint: true
});
```

## Selector Đa Phần Tử

### Or Selector

Lựa chọn một trong nhiều loại phần tử:

```typescript
export function or(
    selectors: ElementSelector<SelectableType>[], 
    options?: MultiSelectorOptions
): OrSelector {
    return new OrSelector(selectors, options);
}

// Ví dụ sử dụng
const pointOrLineSelector = or([
    vertex({ preview: true }),
    stroke({ selectableStrokeTypes: ['RenderLine'] })
], { flatten: true });

const anyElementSelector = or([
    vertex({ realEl: true }),
    stroke({ selectableStrokeTypes: ['RenderLine', 'RenderCircle'] })
]);
```

### Then Selector

Lựa chọn tuần tự các phần tử:

```typescript
export function then(
    selectors: ElementSelector<SelectableType>[],
    options?: MultiSelectorOptions
): ThenSelector {
    return new ThenSelector(selectors, options);
}

// Ví dụ sử dụng
const lineFromPointsSelector = then([
    vertex({ preview: true }),
    vertex({ preview: true })
]);

const perpendicularLineSelector = then([
    stroke({ selectableStrokeTypes: ['RenderLine'] }),
    vertex({ preview: true })
]);
```

### Repeat Selector

Lựa chọn lặp lại với ràng buộc số lượng:

```typescript
export function repeat(
    selector: ElementSelector<SelectableType>,
    options?: RepeatSelectorOptions
): RepeatSelector {
    return new RepeatSelector(selector, options);
}

// Ví dụ sử dụng
const triangleVerticesSelector = repeat(vertex({ preview: true }), {
    minCount: 3,
    maxCount: 3
});

const polygonVerticesSelector = repeat(vertex({ preview: true }), {
    minCount: 3,
    maxCount: 10
});
```

## Mẫu Selector Nâng Cao

### Lựa Chọn Có Điều Kiện

Logic điều kiện phức tạp:

```typescript
const conditionalSelector = or([
    // Đầu tiên thử lựa chọn điểm hiện có
    vertex({ realEl: true }),
    // Sau đó cho phép tạo điểm mới
    vertex({ preview: true })
], {
    onComplete: (selector, doc) => {
        const selected = selector.selected;
        if (selected instanceof RenderVertex && selected.relIndex >= 0) {
            console.log('Đã chọn điểm hiện có');
        } else {
            console.log('Đã tạo điểm mới');
        }
    }
});
```

### Lựa Chọn Có Lọc

Lựa chọn với bộ lọc tùy chỉnh:

```typescript
const filteredSelector = vertex({
    realEl: true,
    refinedFilter: (el: GeoRenderElement) => {
        // Chỉ chọn điểm không ở gốc tọa độ
        const vertex = el as RenderVertex;
        return vertex.x !== 0 || vertex.y !== 0;
    }
});
```

### Lựa Chọn Dính

Duy trì lựa chọn qua các tương tác:

```typescript
const stickyLineSelector = stroke({
    selectableStrokeTypes: ['RenderLine'],
    stickyOnMatch: true,
    stickyPTypes: ['mouse', 'pen'] // Dính với đầu vào chuột và bút
});
```

## Ví Dụ DSL Selector

### Xây Dựng Tam Giác

```typescript
export class CreateTriangleTool extends GeometryTool<CommonToolState> {
    private selector = repeat(vertex({ preview: true }), {
        minCount: 3,
        maxCount: 3,
        onComplete: (selector, doc) => {
            this.createTriangle(selector.selected);
        }
    });
    
    onPointerDown(event: GeoPointerEvent): void {
        this.selector.trySelect(event, this.focusDocCtrl);
    }
}
```

### Công Cụ Điểm Giao

```typescript
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    private selector = repeat(stroke({
        selectableStrokeTypes: ['RenderLine', 'RenderCircle', 'RenderEllipse']
    }), {
        minCount: 2,
        maxCount: 2,
        onComplete: (selector, doc) => {
            this.createIntersectionPoints(selector.selected);
        }
    });
    
    onPointerDown(event: GeoPointerEvent): void {
        this.selector.trySelect(event, this.focusDocCtrl);
    }
}
```

### Góc Bằng Ba Điểm

```typescript
export class CreateAngleByThreePointsTool extends GeometryTool<CommonToolState> {
    private selector = then([
        vertex({ preview: true }), // Đỉnh của góc
        vertex({ preview: true }), // Điểm tia thứ nhất
        vertex({ preview: true })  // Điểm tia thứ hai
    ], {
        onComplete: (selector, doc) => {
            this.createAngle(selector.selected);
        }
    });
    
    onPointerDown(event: GeoPointerEvent): void {
        this.selector.trySelect(event, this.focusDocCtrl);
    }
}
```

## Quản Lý Trạng Thái Selector

### Vòng Đời Lựa Chọn

1. **Khởi Tạo**: Selector được tạo với các tùy chọn
2. **Kiểm Tra Va Chạm**: `tryHit()` kiểm tra các phần tử khớp
3. **Preview**: `tryPreview()` tạo các phần tử preview
4. **Chấp Nhận**: Phần tử được chấp nhận và đánh dấu
5. **Hoàn Thành**: Callback `onComplete` được kích hoạt
6. **Đặt Lại**: Selector được đặt lại cho tương tác tiếp theo

### Thuộc Tính Trạng Thái

```typescript
interface SelectorState {
    selected?: SelectableType;      // Phần tử hiện được chọn
    acceptMarked: boolean;          // Liệu lựa chọn có được chấp nhận
    markedSelect?: SelectableType;  // Lựa chọn được đánh dấu
    isAccepted: boolean;           // Trạng thái chấp nhận
    isPartial: boolean;            // Lựa chọn một phần (multi-selector)
}
```

## Tối Ưu Hóa Hiệu Suất

### Caching

Các selector sử dụng caching để tăng hiệu suất:

```typescript
export class VertexSelector extends ElementSelector<RenderVertex> {
    private curPreviewEl?: RenderVertex;
    private lastHitEl?: RenderVertex;
    private curDoc?: GeoDocCtrl;
    
    // Cache kết quả hit để tránh tính toán dư thừa
    tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): RenderVertex | undefined {
        if (this.lastHitEl && this.curDoc === doc) {
            // Sử dụng kết quả cache nếu điều kiện khớp
            return this.lastHitEl;
        }
        // Thực hiện kiểm tra hit...
    }
}
```

## Thực Hành Tốt Nhất

1. **Sử Dụng Loại Selector Phù Hợp**: Chọn selector phù hợp cho từng trường hợp
2. **Xử Lý Trạng Thái Lựa Chọn**: Quản lý trạng thái selector đúng cách
3. **Cung Cấp Phản Hồi Trực Quan**: Sử dụng preview và cursor phù hợp
4. **Sử Dụng Bộ Lọc Khôn Ngoan**: Áp dụng bộ lọc hiệu quả
5. **Tối Ưu Hóa Hiệu Suất**: Cache các tính toán đắt đỏ
