# Objects System Documentation

## Overview

The objects system manages document controllers, hit detection, and geometric object interactions. It provides the core infrastructure for document management, element selection, and user interaction handling.

## Architecture

### Objects Components

```
objects/
├── geo.document.ctrl.ts    # Document controller and management
└── hit.checker.ts          # Hit detection and selection
```

## Document Controller

### GeoDocCtrl Class

The main document controller that manages geometric documents:

```typescript
export class GeoDocCtrl extends DefaultVDocCtrl implements HasBoundaryCtrl {
    rendererCtrl: GeoRenderer;
    
    private readonly _docRenderProp$: BehaviorSubject<DocRenderProp>;
    private readonly _selectedElements$: BehaviorSubject<GeoRenderElement[]>;
    public readonly selectedElements$: Observable<GeoRenderElement[]>;
    
    constructor(
        public override editor: GeometryEditor,
        public override state: GeoDoc,
        public override viewport: BoardViewportManager
    ) {
        super(state, editor, viewport);
        
        this._docRenderProp$ = new BehaviorSubject<DocRenderProp>(state.docRenderProp);
        this._selectedElements$ = new BehaviorSubject<GeoRenderElement[]>([]);
        this.selectedElements$ = this._selectedElements$.asObservable();
    }
}
```

### Document Properties

```typescript
interface DocumentProperties {
    // Document render properties
    get docRenderProp$(): Observable<DocRenderProp>;
    get docRenderProp(): DocRenderProp;
    set docRenderProp(value: DocRenderProp);
    
    // Default element render properties
    get docDefaultElRenderProps(): DefaultGeoRenderProp;
    set docDefaultElRenderProps(value: DefaultGeoRenderProp);
    
    // Selected elements
    get selectedElements(): GeoRenderElement[];
    get selectedElements$(): Observable<GeoRenderElement[]>;
}
```

### Renderer Management

```typescript
// Attach renderer to document
attachRenderer(renderer: GeoRenderer): void {
    this.rendererCtrl = renderer;
    this.layers.push(renderer.layer);
    this.state.layers.push(renderer.layer.state as GeoLayer);
}

// Remove renderer
onRemove(): void {
    const idx = this.layers.indexOf(this.rendererCtrl.layer);
    this.state.layers.splice(idx, 1);
    this.layers.splice(idx, 1);
    this.rendererCtrl.onRemoved();
    this.viewport.removeLayer(this.rendererCtrl.layer);
}
```

### Document State Management

```typescript
// Build render state for synchronization
buildGeoRenderDocState(): GeoRenderDocState {
    return {
        docId: this.state.globalId,
        canvasWidth: this.rendererCtrl.width,
        canvasHeight: this.rendererCtrl.height,
        numDim: this.state.kind,
        ...this.state.docRenderProp,
    } as GeoRenderDocState;
}

// Update document boundary
updateBoundary(boundary: BoundaryRectangle): void {
    for (let i = 0; i < this.state.layers.length; i++) {
        const layer = this.layers[i];
        this.state.layers[i].boundary = boundary;
        
        if (this.isBoundedView()) {
            (layer as BoundedGraphicLayerCtrl).updateBoundary(boundary);
        }
    }
}
```

## Hit Detection System

### Hit Context Models

```typescript
export interface GeoHitContextDetails extends SelectHitContextDetails {
    el: GeoRenderElement;
    hitMargin: number;
    epsilon?: number;
}

export interface GeoSelectHitContext extends SelectHitContext {
    doc: GeoDocCtrl;
    hitDetails: GeoHitContextDetails;
}
```

### Hit Detection Functions

#### Element Boundary Hit Detection

```typescript
export function checkHitOnElementBoundary2D(
    posInGeo: Position,
    el: GeoRenderElement,
    renderer: GeoRenderer,
    doc: GeoDocCtrl,
    zoomLevel: number,
    epsilon: number
): GeoSelectHitContext | undefined {
    const docRenderProp = doc.state.docRenderProp;
    const lineWeight = el.renderProp['lineWeight'] ?? 1;
    const e = EPSILON(lineWeight / 2, docRenderProp.screenUnit * docRenderProp.scale, zoomLevel) + epsilon;
    
    switch (el.type) {
        case 'RenderVertex':
            return checkHitOnVertex2D(posInGeo, el as RenderVertex, doc, e);
        case 'RenderLine':
        case 'RenderLineSegment':
        case 'RenderVector':
        case 'RenderRay':
            return checkHitOnLine2D(posInGeo, el as RenderLine, doc, renderer, e);
        case 'RenderCircle':
            return checkHitOnCircle2D(posInGeo, el as RenderCircle, doc, e);
        case 'RenderEllipse':
            return checkHitOnEllipse2D(posInGeo, el as RenderEllipse, doc, renderer, e);
        case 'RenderSector':
            return checkHitOnSector2D(posInGeo, el as RenderSector, doc, renderer, e);
        default:
            return undefined;
    }
}
```

#### Batch Hit Detection

```typescript
export function checkHitOnElementsBoundary2D(
    doc: GeoDocCtrl,
    els: GeoRenderElement[],
    posInGeo: Position,
    renderer: GeoRenderer,
    zoomLevel: number,
    epsilon?: number
): GeoSelectHitContext[] {
    return els
        .filter(el => doc.editor.filterElementFunc(el) && (el as StrokeType).length !== undefined)
        .sort((a, b) => ((a as StrokeType).length > (b as StrokeType).length ? 1 : -1))
        .map(el => checkHitOnElementBoundary2D(posInGeo, el, renderer, doc, zoomLevel, epsilon))
        .filter(h => h && true);
}

export function checkHitOnPoints(
    doc: GeoDocCtrl,
    els: GeoRenderElement[],
    posInGeo: Position,
    renderer: GeoRenderer,
    zoomLevel: number,
    epsilon?: number
): GeoSelectHitContext[] {
    return els
        .filter(el => doc.editor.filterElementFunc(el) && el instanceof RenderVertex)
        .map(el => checkHitOnElementBoundary2D(posInGeo, el, renderer, doc, zoomLevel, epsilon))
        .filter(h => h && true);
}
```

### Specific Hit Detection

#### Vertex Hit Detection

```typescript
function checkHitOnVertex2D(
    posInGeo: Position,
    vertex: RenderVertex,
    doc: GeoDocCtrl,
    epsilon: number
): GeoSelectHitContext | undefined {
    const distance = Math.sqrt(
        Math.pow(posInGeo.x - vertex.x, 2) + 
        Math.pow(posInGeo.y - vertex.y, 2)
    );
    
    if (distance <= epsilon) {
        return {
            doc: doc,
            hitDetails: {
                hitId: vertex.relIndex,
                el: vertex,
                hitMargin: distance,
                epsilon: epsilon,
            }
        };
    }
    
    return undefined;
}
```

#### Line Hit Detection

```typescript
function checkHitOnLine2D(
    posInGeo: Position,
    line: RenderLine,
    doc: GeoDocCtrl,
    renderer: GeoRenderer,
    epsilon: number
): GeoSelectHitContext | undefined {
    // Calculate distance from point to line
    const distance = Math.abs(
        line.a * posInGeo.x + line.b * posInGeo.y + line.c
    ) / Math.sqrt(line.a * line.a + line.b * line.b);
    
    if (distance <= epsilon) {
        // For line segments, check if point is within segment bounds
        if (line.type === 'LineSegment') {
            const startPoint = line.startPoint(renderer);
            const endPoint = line.endPoint(renderer);
            
            if (!isPointOnSegment(posInGeo, startPoint, endPoint)) {
                return undefined;
            }
        }
        
        return {
            doc: doc,
            hitDetails: {
                hitId: line.relIndex,
                el: line,
                hitMargin: distance,
                epsilon: epsilon,
            }
        };
    }
    
    return undefined;
}
```

#### Circle Hit Detection

```typescript
function checkHitOnCircle2D(
    posInGeo: Position,
    circle: RenderCircle,
    doc: GeoDocCtrl,
    epsilon: number
): GeoSelectHitContext | undefined {
    const centerDistance = Math.sqrt(
        Math.pow(posInGeo.x - circle.centerX, 2) + 
        Math.pow(posInGeo.y - circle.centerY, 2)
    );
    
    const radiusDistance = Math.abs(centerDistance - circle.radius);
    
    if (radiusDistance <= epsilon) {
        return {
            doc: doc,
            hitDetails: {
                hitId: circle.relIndex,
                el: circle,
                hitMargin: radiusDistance,
                epsilon: epsilon,
            }
        };
    }
    
    return undefined;
}
```

### Inside Hit Detection

#### Polygon Inside Detection

```typescript
function checkHitInsidePolygon2D(
    posInGeo: Position,
    polygon: RenderPolygon,
    doc: GeoDocCtrl,
    renderer: GeoRenderer
): GeoSelectHitContext | undefined {
    const vertices = polygon.vertices(renderer);
    
    if (isPointInsidePolygon(posInGeo, vertices)) {
        return {
            doc: doc,
            hitDetails: {
                hitId: polygon.relIndex,
                el: polygon,
                hitMargin: 0,
            }
        };
    }
    
    return undefined;
}

function isPointInsidePolygon(point: Position, vertices: Point[]): boolean {
    let inside = false;
    const n = vertices.length;
    
    for (let i = 0, j = n - 1; i < n; j = i++) {
        const vi = vertices[i];
        const vj = vertices[j];
        
        if (((vi.y > point.y) !== (vj.y > point.y)) &&
            (point.x < (vj.x - vi.x) * (point.y - vi.y) / (vj.y - vi.y) + vi.x)) {
            inside = !inside;
        }
    }
    
    return inside;
}
```

#### Circle Inside Detection

```typescript
function checkHitInsideCircle2D(
    posInGeo: Position,
    circle: RenderCircleShape,
    doc: GeoDocCtrl,
    renderer: GeoRenderer
): GeoSelectHitContext | undefined {
    const distance = Math.sqrt(
        Math.pow(posInGeo.x - circle.centerX, 2) + 
        Math.pow(posInGeo.y - circle.centerY, 2)
    );
    
    if (distance <= circle.radius) {
        return {
            doc: doc,
            hitDetails: {
                hitId: circle.relIndex,
                el: circle,
                hitMargin: circle.radius - distance,
            }
        };
    }
    
    return undefined;
}
```

## Document Hit Detection

### Main Hit Detection Method

```typescript
checkHit(
    event: LocatableEvent<any>,
    layer: BoundedGraphicLayerCtrl,
    useRelaxedHitPrecision = false,
    preview = false
): GeoSelectHitContext {
    const isTouchEvent = event.nativeEvent['pointerType'] == 'touch';
    const posInGeo = calculatePosInLayer(event, this);
    
    if (!validatePointerPos(posInGeo, this)) {
        return undefined;
    }
    
    const zoomLevel = calculateZoomLevel(this, this.viewport);
    const epsilon = useRelaxedHitPrecision ? 
        (isTouchEvent ? TOUCH_HIT_EPSILON : RELAXED_HIT_EPSILON) : 
        (isTouchEvent ? TOUCH_HIT_EPSILON : NORMAL_HIT_EPSILON);
    
    const elements = preview ? 
        this.rendererCtrl.previewObjects.allElements() :
        this.rendererCtrl.originObjects.allElements();
    
    // Check points first (highest priority)
    const pointHits = checkHitOnPoints(
        this, elements, posInGeo, this.rendererCtrl, zoomLevel, epsilon
    );
    if (pointHits.length > 0) {
        return pointHits[0];
    }
    
    // Check element boundaries
    const boundaryHits = checkHitOnElementsBoundary2D(
        this, elements, posInGeo, this.rendererCtrl, zoomLevel, epsilon
    );
    if (boundaryHits.length > 0) {
        return boundaryHits[0];
    }
    
    // Check inside elements (lowest priority)
    const insideHits = checkHitOnElementsInside2D(
        this, elements, posInGeo, this.rendererCtrl
    );
    if (insideHits.length > 0) {
        return insideHits[0];
    }
    
    return undefined;
}
```

## Usage Examples

### Document Creation and Management

```typescript
// Create document controller
const docCtrl = new GeoDocCtrl(editor, geoDoc, viewport);

// Attach renderer
const renderer = new Geo2dRenderer(layerCtrl, docCtrl);
docCtrl.attachRenderer(renderer);

// Subscribe to document changes
docCtrl.docRenderProp$.subscribe(renderProp => {
    console.log('Document render properties changed:', renderProp);
});

docCtrl.selectedElements$.subscribe(elements => {
    console.log('Selected elements changed:', elements);
});
```

### Hit Detection in Tools

```typescript
export class SelectionTool extends GeometryTool<CommonToolState> {
    onPointerDown(event: GeoPointerEvent): void {
        const hitContext = this.focusDocCtrl.checkHit(
            event, 
            this.focusDocCtrl.rendererCtrl.layer,
            false, // Use normal precision
            false  // Check real elements
        );
        
        if (hitContext) {
            this.handleElementHit(hitContext);
        } else {
            this.handleEmptySpaceClick(event);
        }
    }
    
    private handleElementHit(hitContext: GeoSelectHitContext): void {
        const element = hitContext.hitDetails.el;
        const distance = hitContext.hitDetails.hitMargin;
        
        console.log(`Hit element ${element.relIndex} at distance ${distance}`);
        
        // Update selection
        this.focusDocCtrl.selectedElements = [element];
    }
}
```

### Custom Hit Detection

```typescript
// Custom hit detection for specific element types
function checkHitOnCustomElement(
    posInGeo: Position,
    element: CustomRenderElement,
    doc: GeoDocCtrl,
    epsilon: number
): GeoSelectHitContext | undefined {
    // Implement custom hit detection logic
    const isHit = customHitTestLogic(posInGeo, element, epsilon);
    
    if (isHit) {
        return {
            doc: doc,
            hitDetails: {
                hitId: element.relIndex,
                el: element,
                hitMargin: calculateHitMargin(posInGeo, element),
                epsilon: epsilon,
            }
        };
    }
    
    return undefined;
}
```

### Batch Operations

```typescript
// Select multiple elements in a region
function selectElementsInRegion(
    doc: GeoDocCtrl,
    region: Rectangle
): GeoRenderElement[] {
    const elements = doc.rendererCtrl.originObjects.allElements();
    const selected: GeoRenderElement[] = [];
    
    for (const element of elements) {
        if (isElementInRegion(element, region)) {
            selected.push(element);
        }
    }
    
    doc.selectedElements = selected;
    return selected;
}

function isElementInRegion(element: GeoRenderElement, region: Rectangle): boolean {
    switch (element.type) {
        case 'RenderVertex':
            const vertex = element as RenderVertex;
            return region.contains(vertex.x, vertex.y);
        case 'RenderLine':
            // Check if line intersects region
            return lineIntersectsRectangle(element as RenderLine, region);
        default:
            return false;
    }
}
```

## Performance Optimization

### Hit Detection Optimization

```typescript
// Use spatial indexing for large numbers of elements
class SpatialIndex {
    private quadTree: QuadTree;
    
    constructor(boundary: Rectangle) {
        this.quadTree = new QuadTree(boundary);
    }
    
    insert(element: GeoRenderElement): void {
        const bounds = this.getElementBounds(element);
        this.quadTree.insert(element, bounds);
    }
    
    query(region: Rectangle): GeoRenderElement[] {
        return this.quadTree.query(region);
    }
}

// Optimized hit detection using spatial index
function optimizedHitDetection(
    posInGeo: Position,
    doc: GeoDocCtrl,
    epsilon: number
): GeoSelectHitContext | undefined {
    const searchRegion = new Rectangle(
        posInGeo.x - epsilon, posInGeo.y - epsilon,
        2 * epsilon, 2 * epsilon
    );
    
    const candidates = doc.spatialIndex.query(searchRegion);
    
    for (const element of candidates) {
        const hit = checkHitOnElementBoundary2D(
            posInGeo, element, doc.rendererCtrl, doc, 1.0, epsilon
        );
        if (hit) return hit;
    }
    
    return undefined;
}
```

## Best Practices

1. **Hit Priority**: Check points before lines, lines before shapes
2. **Epsilon Values**: Use appropriate epsilon values for different input types
3. **Performance**: Use spatial indexing for large numbers of elements
4. **Precision**: Consider zoom level when calculating hit tolerances
5. **Validation**: Always validate pointer positions before hit testing
6. **Caching**: Cache hit detection results when appropriate
