# Tài Liệu Tham Khảo API

## Tổng Quan

Tài liệu này cung cấp tham khảo API toàn diện cho Trình Soạn Thảo Hình Học Viclass. <PERSON><PERSON> bao gồm các lớp, giao diện và hàm chính có sẵn để mở rộng và tích hợp với trình soạn thảo hình học.

## API Cốt Lõi

### GeometryEditor

Lớp trình soạn thảo chính điều phối tất cả các hoạt động hình học.

```typescript
class GeometryEditor extends EditorBase<GeoDocCtrl> {
    // Thuộc tính
    readonly geoEditorConf: GeoEditorConfig;
    readonly operationMode: OperationMode;
    readonly geoGateway: GeoGateway;
    
    // Tính năng
    selectionFeature: SelectionFeature;
    panFeature: PanFeature;
    zoomFeature: ZoomFeature;
    awarenessFeature: AwarenessFeature;
    
    // Phương thức
    createDocument(initData: GeoDocInitData): Promise<GeoDocCtrl>;
    createToolbar(): ToolBar<any, any>;
    start(): Promise<void>;
    filterElementFunc(el: GeoRenderElement): boolean;
}
```

### GeoDocCtrl

Bộ điều khiển tài liệu để quản lý tài liệu hình học.

```typescript
class GeoDocCtrl extends DefaultVDocCtrl implements HasBoundaryCtrl {
    // Thuộc tính
    rendererCtrl: GeoRenderer;
    readonly selectedElements$: Observable<GeoRenderElement[]>;
    readonly docRenderProp$: Observable<DocRenderProp>;
    
    // Phương thức
    attachRenderer(renderer: GeoRenderer): void;
    checkHit(event: LocatableEvent<any>, layer: BoundedGraphicLayerCtrl, 
             useRelaxedHitPrecision?: boolean, preview?: boolean): GeoSelectHitContext;
    updateBoundary(boundary: BoundaryRectangle): void;
    buildGeoRenderDocState(): GeoRenderDocState;
}
```

### GeoRenderer

Lớp cơ sở trừu tượng cho renderer hình học.

```typescript
abstract class GeoRenderer {
    // Thuộc tính
    readonly docCtrl: GeoDocCtrl;
    readonly layer: GraphicLayerCtrl;
    protected originObjects: GeoObjCollection;
    protected previewObjects: GeoObjCollection;
    
    // Phương thức
    abstract render(): void;
    abstract clearBoard(): void;
    
    addActualElement(element: GeoRenderElement): void;
    addPreviewElement(element: GeoRenderElement): void;
    removePreviewByIds(previewEls: (GeoRenderElement | number)[]): void;
    elementAt<T extends GeoRenderElement>(relIndex: number): T | undefined;
    
    // Biến đổi tọa độ
    geoToLayerX(x: number): number;
    geoToLayerY(y: number): number;
    layerToGeoX(x: number): number;
    layerToGeoY(y: number): number;
}
```

## API Công Cụ

### GeometryTool

Lớp cơ sở cho tất cả công cụ hình học.

```typescript
abstract class GeometryTool<T extends ToolState> extends Tool<T, GeometryToolBar> {
    // Thuộc tính
    protected focusDocCtrl: GeoDocCtrl;
    protected editor: GeometryEditor;
    
    // Phương thức trừu tượng
    abstract onPointerDown(event: GeoPointerEvent): void;
    
    // Phương thức tùy chọn
    onPointerMove?(event: GeoPointerEvent): void;
    onPointerUp?(event: GeoPointerEvent): void;
    onKeyDown?(event: GeoKeyboardEvent): void;
    onActivate?(): void;
    onDeactivate?(): void;
}
```

### Giao Diện Trạng Thái Công Cụ

```typescript
interface ToolState {}

class CommonToolState implements ToolState {}

class TriangleToolState extends CommonToolState {
    drawMode: number;
}

class QuadToolState extends CommonToolState {
    drawMode: number;
}

class RegularPolygonToolState extends CommonToolState {
    sideCount: number;
    drawMode: number;
}
```

## API Selector

### ElementSelector

Lớp cơ sở cho selector phần tử.

```typescript
abstract class ElementSelector<T extends SelectableType, OT extends SelectorOptions<T>> {
    // Thuộc tính
    abstract multiple: boolean;
    selected?: T;
    acceptMarked: boolean;
    
    // Phương thức
    abstract tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;
    abstract tryPreview(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;
    abstract clearTrial(doc: GeoDocCtrl): void;
    
    trySelect(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;
    accept(el: T, doc: GeoDocCtrl): void;
    reset(keepPreview?: boolean): void;
}
```

### Hàm Factory Selector

```typescript
// Selector cơ bản
function vertex(options?: VertexSelectorOptions): VertexSelector;
function stroke(options?: StrokeSelectorOptions): StrokeSelector;
function vertexOnStroke(options?: VertexOnStrokeSelectorOptions): VertexOnStrokeSelector;

// Selector đa phần tử
function or(selectors: ElementSelector<SelectableType>[], 
           options?: MultiSelectorOptions): OrSelector;
function then(selectors: ElementSelector<SelectableType>[], 
             options?: MultiSelectorOptions): ThenSelector;
function repeat(selector: ElementSelector<SelectableType>, 
               options?: RepeatSelectorOptions): RepeatSelector;
```

### Tùy Chọn Selector

```typescript
interface SelectorOptions<T extends SelectableType> {
    preview?: boolean;
    realEl?: boolean;
    autoAccept?: boolean;
    stickyOnMatch?: boolean;
    cursor?: BehaviorSubject<Cursor[]>;
    refinedFilter?: (el: GeoRenderElement) => boolean;
    onComplete?: (selector: ElementSelector<T>, doc: GeoDocCtrl) => void;
    onReset?: (selector: ElementSelector<T>) => void;
}

interface VertexSelectorOptions extends SelectorOptions<RenderVertex> {
    stickyPTypes?: PointerType[];
}

interface StrokeSelectorOptions<T extends SelectableType> extends SelectorOptions<T> {
    selectableStrokeTypes?: GeoRelType[];
    cfunc?: (el: StrokeType, doc: GeoDocCtrl) => boolean;
}

interface RepeatSelectorOptions<T extends SelectableType[]> extends MultiSelectorOptions<T> {
    minCount?: number;
    maxCount?: number;
}
```

## API Model

### Phần Tử Render

```typescript
abstract class GeoRenderElement {
    abstract readonly type: GeoRelType;
    abstract readonly elType: GeoObjectType;
    abstract renderProp?: GeoRenderProp;
    
    relIndex: number;
    name: string;
    usable: boolean;
    deleted?: boolean;
    valid: boolean;
    pInfo?: GeoPreviewInfo;
}

class RenderVertex extends GeoRenderElement {
    readonly type: GeoRelType = 'Vertex';
    readonly elType: GeoObjectType = 'Point';
    
    x: number;
    y: number;
    renderProp?: VertexGeoRenderProp;
}

class RenderLine extends GeoRenderElement {
    readonly type: GeoRelType = 'Line';
    readonly elType: GeoObjectType = 'LineVi';
    
    a: number; // Phương trình đường thẳng: ax + by + c = 0
    b: number;
    c: number;
    renderProp?: LineGeoRenderProp;
    
    vector(renderer?: GeoRenderer): number[];
    distanceToPoint(x: number, y: number): number;
}
```

### Bộ Sưu Tập Đối Tượng

```typescript
class GeoObjCollection {
    // Quản lý phần tử
    addRenderElement(element: GeoRenderElement): void;
    removeByIds(ids: number[]): void;
    elementAt<T extends GeoRenderElement>(relIndex: number): T | undefined;
    
    // Truy vấn
    vertices(highlighted?: boolean): RenderVertex[];
    lines(highlighted?: boolean): RenderLine[];
    circles(highlighted?: boolean): RenderCircle[];
    polygons(highlighted?: boolean): RenderPolygon[];
    allElements(): GeoRenderElement[];
    
    // Quản lý trạng thái
    highlight(elIndexArr: number[]): void;
    removeHighlight(elIndexArr: number[]): void;
    select(elIndexArr: number[]): void;
    
    // Thuộc tính
    readonly size: number;
}
```

## API Toán Học

### Số Phức

```typescript
class Complex {
    static EPSILON: number;
    
    constructor(re?: number, im?: number);
    
    // Thuộc tính
    get real(): number;
    get imag(): number;
    
    // Phương thức factory
    static fromRI(re: number, im: number): Complex;
    static fromPolar(magnitude: number, angle: number): Complex;
    static fromZ(z: Complex): Complex;
    
    // Phép toán số học
    add(n: number | Complex): Complex;
    sub(n: number | Complex): Complex;
    mult(n: number | Complex): Complex;
    div(n: number | Complex): Complex;
    
    // Phép toán nâng cao
    abs(): number;
    arg(): number;
    pow(n: number): Complex;
    sqrt(): Complex;
    exp(): Complex;
    log(): Complex;
    roots(n: number): Complex[];
    
    // Phương thức tiện ích
    equals(z: Complex, epsilon?: number): boolean;
    isReal(epsilon?: number): boolean;
    isZero(epsilon?: number): boolean;
    toString(): string;
}
```

### Giải Đa Thức

```typescript
abstract class PolyBase {
    static getPoly(coefs: number[]): PolyBase;
    
    roots(): Complex[];
    eval(x: number | Complex): Complex;
}

class Linear extends PolyBase {
    constructor(coefs: number[]); // [a1, a0] cho a1*x + a0 = 0
}

class Quadratic extends PolyBase {
    constructor(coefs: number[]); // [a2, a1, a0] cho a2*x^2 + a1*x + a0 = 0
}

class Cubic extends PolyBase {
    constructor(coefs: number[]); // [a3, a2, a1, a0]
}

class Quartic extends PolyBase {
    constructor(coefs: number[]); // [a4, a3, a2, a1, a0]
}
```

### Hệ Số

```typescript
class Coefficients {
    a: number; b: number; c: number;
    d: number; e: number; f: number;
    
    constructor(a: number, b: number, c: number, d: number, e: number, f: number);
    
    // Phép toán
    add(other: Coefficients): Coefficients;
    subtract(other: Coefficients): Coefficients;
    mult(scalar: number): Coefficients;
    divide(scalar: number): Coefficients;
    
    // Tiện ích
    getCoef(name: string): number;
    copy(): Coefficients;
    
    // Phương thức tĩnh
    static elliminateTerm(s: Coefficients, t: Coefficients, coef: string): Coefficients;
}
```

## API Hướng

### Hàm Tính Toán Hướng

```typescript
// Hướng dựa trên đường
function nthDirectionByLine(vec: number[], role: number[], p: number[]): number;
function nthSideOfVector(ps: Point[], p1Idx: number, p2Idx: number, p3Idx: number, 
                        p1RelIdx: number, p2RelIdx: number): number;

// Hướng xoay
function nthDirectionRotation(vec: number[], role: number[], p0: number[], 
                             points: number[][]): number;
function sortByRotationV2(role: Point, points: Point[], vec?: Vector): Point[];

// Hướng tuyến tính
function nthDirectionOnLine(vec: number[], p0: number[], points: number[][]): number;

// So sánh hướng
function directionOfPointOnParallelVector(vec: number[], root: number[], p: number[]): 1 | -1;
function isLargerThan(sourceRelIdx: number, targetRelIdx: number): boolean;
```

## API Gateway

### GeoGateway

Gateway giao tiếp API cho các hoạt động hình học.

```typescript
class GeoGateway {
    constructor(backendUri: string);
    
    // Hoạt động tài liệu
    createDoc(numDim: number, width: number, height: number, unit: number): Promise<FetchDocResponse>;
    fetchDoc(docGlobalId: DocumentId): Promise<FetchDocResponse>;
    
    // Hoạt động xây dựng
    construct(docGlobalId: DocumentId, constructions: ConstructionRequest[], 
             preview?: boolean): Promise<ApplyConstructionResponse>;
    reconstruct(docGlobalId: DocumentId, ctIdx: number, 
               construction: GeoElConstructionRequest): Promise<ReconstructionResponse>;
    
    // Hoạt động phần tử
    moveElement(docGlobalId: DocumentId, ctIdx: number, 
               construction: GeoElConstructionRequest): Promise<MoveElementResponse>;
    updateElementState(docGlobalId: DocumentId, 
                      request: UpdateElementStateRequest): Promise<void>;
    renameElement(docGlobalId: DocumentId, 
                 renameModel: RenameElementModel): Promise<void>;
    
    // Hoạt động tiện ích
    nameByLang(textIds: string[]): Promise<Map<string, string>>;
    constraintTemplates(template: string): Promise<ConstraintTemplateData>;
}
```

### Mô Hình Xây Dựng

```typescript
interface GeoElConstructionRequest {
    ctId: string;
    elType: GeoObjectType;
    cgName: string;
    paramSpecs?: ParamSpecs[];
    name?: string;
    ctIdx?: number;
}

interface ConstructionRequest {
    construction: GeoElConstructionRequest;
    renderProp?: GeoRenderProp;
}

interface ParamSpecs {
    paramKind: ParamKind;
    value?: number;
    name?: string;
    expression?: string;
    relIdx?: number;
    relType?: RelType;
}

enum ParamKind {
    'value',
    'name',
    'expression',
}
```

## API Tiện Ích

### Tiện Ích Preview

```typescript
class PreviewQueue {
    add(...elements: GeoRenderElement[]): void;
    flush(docCtrl: GeoDocCtrl): Promise<void>;
    clear(): void;
}

// Tạo phần tử preview
function pVertex(x: number, y: number, id: number): RenderVertex;
function pLine(a: number, b: number, c: number, id: number): RenderLine;
function pCircle(cx: number, cy: number, r: number, id: number): RenderCircle;
function pAngle(vertex: Point, start: Point, end: Point, id: number): RenderAngle;
```

### Tiện Ích Công Cụ

```typescript
// Tiện ích xây dựng
function remoteConstruct(editor: GeometryEditor, constructions: ConstructionRequest[], 
                        docCtrl: GeoDocCtrl): Promise<ApplyConstructionResponse>;
function buildPointConstruction(vertex: RenderVertex): GeoElConstructionRequest;
function buildLineConstruction(vertices: RenderVertex[]): GeoElConstructionRequest;

// Tiện ích tọa độ
function calculatePosInLayer(event: LocatableEvent<any>, docCtrl: GeoDocCtrl): Position;
function validatePointerPos(pos: Position, docCtrl: GeoDocCtrl): boolean;

// Tiện ích đặt tên
function assignNames(elements: GeoRenderElement[], pattern: string): void;
function validateName(name: string, elementType: GeoRelType): boolean;
```

## Định Nghĩa Kiểu

### Kiểu Cốt Lõi

```typescript
type GeoObjectType = 'Point' | 'Line' | 'LineSegment' | 'Ray' | 'Vector' | 
                    'Circle' | 'Ellipse' | 'Semicircle' | 'CircularSector' |
                    'Triangle' | 'RightTriangle' | 'IsoscelesTriangle' | 'EquilateralTriangle' |
                    'Polygon' | 'RegularPolygon' | 'Quadrilateral' |
                    'Rectangle' | 'Square' | 'Parallelogram' | 'Rhombus' | 'Trapezoid' |
                    'Angle';

type GeoRelType = 'Vertex' | 'Line' | 'LineSegment' | 'Vector' | 'Ray' |
                 'Circle' | 'CircleShape' | 'Ellipse' | 'EllipseShape' |
                 'Sector' | 'SectorShape' | 'Polygon' | 'Angle';

type GeometryToolType = 'CreatePointTool' | 'CreateLineTool' | 'CreateCircleTool' |
                       'CreateTriangleTool' | 'CreateSquareTool' | 'CreateAngleTool' |
                       'IntersectionPointTool' | 'MiddlePointTool' | 'PointOnObjectTool' |
                       'CreateParallelLineTool' | 'CreatePerpendicularLineTool' |
                       'MoveElementTool' | 'UpdatePropTool' | 'RenameElementTool' |
                       'GeoPanTool' | 'GeoZoomTool' | 'InputCommandTool';

type SelectableType = GeoRenderElement | GeoRenderElement[] | SelectableType[] | 'nothing';
```

### Kiểu Sự Kiện

```typescript
type GeoPointerEvent = UIPointerEventData<NativeEventTarget<any>>;
type GeoKeyboardEvent = KeyboardEventData<NativeEventTarget<any>>;
type GeoToolEventData = ToolEventData<GeometryToolBar, GeometryToolType>;
```

## Hằng Số

```typescript
const GeoEpsilon = Math.pow(10, -10);
const RENDER_VERTEX_RADIUS = 3;
const RENDER_VERTEX_RADIUS_POTENTIAL = 5;

const PreviewIds = {
    stroke: -1000000,
    vertex: -10000000,
};
```

Tham khảo API này cung cấp các giao diện và lớp thiết yếu để mở rộng Trình Soạn Thảo Hình Học Viclass. Để biết ví dụ triển khai chi tiết, hãy tham khảo các file tài liệu module cụ thể.
