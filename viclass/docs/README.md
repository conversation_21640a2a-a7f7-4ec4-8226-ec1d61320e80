# Tài Liệu Trình Soạn Thảo Hình Học Viclass

## Tổng Quan

Trình Soạn Thảo Hình Học Viclass là một hệ thống xây dựng và thao tác hình học 2D toàn diện được phát triển bằng TypeScript và Angular. Nó cung cấp một bộ công cụ mạnh mẽ để tạo, chỉnh sửa và giải quyết các bài toán hình học với giao diện trực quan và nền tảng toán học vững chắc.

## Kiến Trúc

Trình soạn thảo hình học tuân theo kiến trúc modular với sự phân tách rõ ràng các mối quan tâm:

```
viclass/packages/editors/viclass/editor.geo/
├── src/lib/
│   ├── tools/           # Công cụ tạo và thao tác hình học
│   ├── selectors/       # Hệ thống lựa chọn phần tử với DSL
│   ├── nth.direction/   # Thuật toán tính toán hướng
│   ├── model/          # Mô hình dữ liệu và cấu trúc
│   ├── solving/        # Thuật toán giải toán học
│   ├── objects/        # Bộ điều khiển tài liệu và phát hiện va chạm
│   ├── renderer/       # Hệ thống render 2D
│   ├── cmd/            # Hệ thống lệnh
│   ├── history/        # Chức năng hoàn tác/làm lại
│   └── extension.function/ # Các hàm mở rộng
```

## Các Thành Phần Cốt Lõi

### 1. Trình Soạn Thảo Hình Học (`geo.editor.ts`)

Lớp trình soạn thảo chính điều phối tất cả các thành phần:

```typescript
export class GeometryEditor extends EditorBase<GeoDocCtrl> implements DocumentEditor, SupportInternalSelectFeature {
    // Các tính năng cốt lõi
    selectionFeature: SelectionFeature;
    panFeature: PanFeature;
    zoomFeature: ZoomFeature;
    awarenessFeature: AwarenessFeature;

    // Quản lý tài liệu
    createDocument(initData: GeoDocInitData): Promise<GeoDocCtrl>;
    createToolbar(): ToolBar<any, any>;
}
```

### 2. Hệ Thống Công Cụ

Bộ công cụ hình học toàn diện để tạo và thao tác các đối tượng:

- **Công Cụ Điểm**: CreatePointTool, MiddlePointTool, IntersectionPointTool
- **Công Cụ Đường**: CreateLineTool, CreateLineSegmentTool, CreateRayTool, CreateVectorTool
- **Công Cụ Hình**: CreateCircleTool, CreateEllipseTool, CreatePolygonTool
- **Công Cụ Tam Giác**: CreateTriangleTool, CreateRightTriangleTool, CreateIsoscelesTriangleTool
- **Công Cụ Tứ Giác**: CreateSquareTool, CreateRectangleTool, CreateParallelogramTool
- **Công Cụ Biến Đổi**: CreateParallelLineTool, CreatePerpendicularLineTool

### 3. Hệ Thống Selector

Lựa chọn phần tử nâng cao với Ngôn Ngữ Đặc Thù Miền (DSL):

```typescript
// Ví dụ sử dụng selector
const vertexSelector = vertex({ preview: true, autoAccept: true });
const strokeSelector = stroke({ selectableStrokeTypes: ['RenderLine'] });
const orSelector = or([vertexSelector, strokeSelector], { flatten: true });
```

### 4. Giải Toán Học

Nền tảng toán học vững chắc cho các tính toán hình học:

- **Giải Đa Thức**: Phương trình Tuyến tính, Bậc hai, Bậc ba, Bậc bốn
- **Số Phức**: Số học phức đầy đủ với các hàm lượng giác
- **Hệ Số**: Thao tác phương trình ellipse

### 5. Hệ Thống Render

Render canvas 2D hiệu suất cao:

```typescript
export class Geo2dRenderer extends GeoRenderer {
    render(): void;
    renderObject(rel: GeoRenderElement, highlight?: boolean): void;
    renderGrid(): void;
}
```

## Các Tính Năng Chính

### Các Loại Đối Tượng Hình Học

Trình soạn thảo hỗ trợ một loạt rộng các đối tượng hình học:

```typescript
export type GeoObjectType =
    | 'Point'
    | 'Line'
    | 'LineSegment'
    | 'Ray'
    | 'Vector'
    | 'Circle'
    | 'Ellipse'
    | 'Semicircle'
    | 'CircularSector'
    | 'Triangle'
    | 'RightTriangle'
    | 'IsoscelesTriangle'
    | 'EquilateralTriangle'
    | 'Polygon'
    | 'RegularPolygon'
    | 'Quadrilateral'
    | 'Rectangle'
    | 'Square'
    | 'Parallelogram'
    | 'Rhombus'
    | 'Trapezoid'
    | 'Angle';
```

### Các Loại Công Cụ

Hệ thống công cụ toàn diện:

```typescript
export type GeometryToolType =
    | 'CreatePointTool'
    | 'CreateLineTool'
    | 'CreateCircleTool'
    | 'CreateTriangleTool'
    | 'CreateSquareTool'
    | 'CreateAngleTool'
    | 'IntersectionPointTool'
    | 'MiddlePointTool'
    | 'PointOnObjectTool'
    | 'CreateParallelLineTool'
    | 'CreatePerpendicularLineTool'
    | 'MoveElementTool'
    | 'UpdatePropTool'
    | 'RenameElementTool'
    | 'GeoPanTool'
    | 'GeoZoomTool'
    | 'InputCommandTool';
```

### DSL Lựa Chọn

Ngôn ngữ lựa chọn mạnh mẽ cho các tương tác phức tạp:

```typescript
// Lựa chọn tuần tự
const thenSelector = then([vertex({ preview: true }), stroke({ selectableStrokeTypes: ['RenderLine'] })]);

// Lựa chọn lặp lại
const repeatSelector = repeat(vertex({ preview: true }), {
    minCount: 3,
    maxCount: 5,
});

// Lựa chọn có điều kiện
const orSelector = or([vertex({ preview: true }), stroke({ selectableStrokeTypes: ['RenderLine', 'RenderCircle'] })]);
```

## Bắt Đầu

### Sử Dụng Cơ Bản

```typescript
// Tạo trình soạn thảo hình học
const editor = new GeometryEditor(config, coordinator);

// Tạo tài liệu
const docCtrl = await editor.createDocument({
    numDim: 2,
    kind: GeoKind.GEO2D,
    boundary: { x: 0, y: 0, width: 800, height: 600 },
    docRenderProp: defaultRenderProp,
});

// Tạo thanh công cụ và các công cụ
const toolbar = editor.createToolbar();
const pointTool = toolbar.getTool('CreatePointTool');
```

### Ví Dụ Triển Khai Công Cụ

```typescript
export class CreatePointTool extends GeometryTool<CommonToolState> {
    private selector = vertex({ preview: true, autoAccept: true });

    onPointerDown(event: GeoPointerEvent): void {
        const selected = this.selector.trySelect(event, this.focusDocCtrl);
        if (selected) {
            this.createPoint(selected);
        }
    }

    private async createPoint(vertex: RenderVertex): Promise<void> {
        const construction = buildPointConstruction(vertex);
        await remoteConstruct(this.editor, [construction], this.focusDocCtrl);
    }
}
```

## Cấu Trúc Tài Liệu

- [Hệ Thống Công Cụ](./tools.md) - Tài liệu chi tiết về công cụ
- [Hệ Thống Selector](./selectors.md) - DSL lựa chọn và các mẫu
- [Thuật Toán Hướng](./nth-direction.md) - Tính toán hướng hình học
- [Mô Hình Dữ Liệu](./model.md) - Mô hình đối tượng và cấu trúc
- [Giải Toán Học](./solving.md) - Giải đa thức và số phức
- [Đối Tượng Tài Liệu](./objects.md) - Bộ điều khiển tài liệu và phát hiện va chạm
- [Hệ Thống Render](./renderer.md) - Render canvas 2D
- [Tham Khảo API](./api-reference.md) - Tài liệu API đầy đủ

## Đóng Góp

Khi mở rộng trình soạn thảo hình học:

1. Tuân theo các mẫu kiến trúc hiện có
2. Sử dụng DSL selector để lựa chọn phần tử nhất quán
3. Triển khai quản lý trạng thái công cụ phù hợp
4. Thêm các bài kiểm tra toàn diện cho thuật toán toán học
5. Tài liệu hóa các tính năng và API mới

## Cân Nhắc Hiệu Suất

- Các bộ sưu tập đối tượng sử dụng cơ chế cache hiệu quả
- Render được tối ưu hóa với viewport culling
- Tính toán toán học sử dụng so sánh dựa trên epsilon
- Các phần tử preview được quản lý riêng biệt với các phần tử thực
