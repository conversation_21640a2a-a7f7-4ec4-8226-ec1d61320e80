# Tài Liệu Nth Direction

## Tổng Quan

Module nth.direction cung cấp các thuật toán để tính toán hướng và vị trí hình học. Nó xử lý các mối quan hệ không gian giữa các điể<PERSON>, đường thẳng và các đối tượng hình họ<PERSON>, đả<PERSON> bảo định vị nhất quán và thứ tự trong các xây dựng hình học.

## Khái Niệm Cốt Lõi

### Tính Toán Hướng

Các thuật toán hướng xác định vị trí tương đối của các điểm và đối tượng trong không gian hình học. Điều này rất quan trọng cho:

- Đặt đối tượng nhất quán
- <PERSON><PERSON> trì các mối quan hệ hình học
- <PERSON><PERSON><PERSON> bả<PERSON> hành vi xây dựng có thể dự đoán
- <PERSON><PERSON> lý các biến đổi đối tượng

### H<PERSON> Tọa Độ

Module hoạt động với hệ tọa độ 2D và xử lý:

- Vị trí điểm dưới dạng mảng `[x, y]`
- Hướng vector dưới dạng mảng `[dx, dy]`
- Đo góc bằng radian
- Định vị tương đối giữa các đối tượng

## Hàm Cốt Lõi

### Hướng Dựa Trên Đường

#### `nthDirectionByLine(vec, role, p)`

Xác định điểm nằm ở phía nào của đường thẳng:

```typescript
export function nthDirectionByLine(vec: number[], role: number[], p: number[]): number {
    const _v = vector(vec[0], vec[1]);
    const _role = point(role[0], role[1]);
    const _p = point(p[0], p[1]);
    const vector_role_p = vector(_role, _p);
    
    if (_v.length === 0 || vector_role_p.length === 0) return 1;
    
    const angle = _v.angleTo(vector_role_p);
    return angle < Math.PI ? 1 : 2;
}
```

**Tham số:**
- `vec`: Vector hướng của đường thẳng
- `role`: Điểm tham chiếu trên đường thẳng
- `p`: Điểm cần kiểm tra

**Trả về:** 1 nếu điểm ở phía thứ nhất, 2 nếu ở phía thứ hai

#### `nthSideOfVector(ps, p1Idx, p2Idx, p3Idx, p1RelIdx, p2RelIdx)`

Xác định điểm nằm ở phía nào của vector, xem xét chỉ số phần tử:

```typescript
export function nthSideOfVector(
    ps: Point[],
    p1Idx: number,
    p2Idx: number,
    p3Idx: number,
    p1RelIdx: number,
    p2RelIdx: number
): number {
    // Xác định điểm nào là điểm đầu tiên dựa trên relIdx
    if ((p1RelIdx >= 0 && p2RelIdx >= 0 && p1RelIdx > p2RelIdx) || 
        (p1RelIdx < 0 && p2RelIdx >= 0)) {
        const temp = p1Idx;
        p1Idx = p2Idx;
        p2Idx = temp;
    }
    
    return nthDirectionByLineV2(vector(ps[p1Idx], ps[p2Idx]), ps[p1Idx], ps[p3Idx]);
}
```

### Hướng Xoay

#### `nthDirectionRotation(vec, role, p0, points)`

Tìm vị trí thứ n của một điểm khi xoay quanh tâm:

```typescript
export function nthDirectionRotation(
    vec: number[], 
    role: number[], 
    p0: number[], 
    points: number[][]
): number {
    const _v = vector(vec[0], vec[1]);
    const _p = new Set<Point>();
    const _p0 = point(p0[0], p0[1]);
    const _role = point(role[0], role[1]);
    
    _p.add(_p0);
    points.forEach(i => {
        if (i !== p0) _p.add(point(i[0], i[1]));
    });
    
    const _s = [..._p].sort((p1, p2) => {
        const angle1 = p1.equalTo(_role) ? 0 : _v.angleTo(vector(_role, p1));
        const angle2 = p2.equalTo(_role) ? 0 : _v.angleTo(vector(_role, p2));
        return angle1 < angle2 ? -1 : angle1 > angle2 ? 1 : 0;
    });
    
    return _s.indexOf(_p0) + 1;
}
```

### Hướng Tuyến Tính

#### `nthDirectionOnLine(vec, p0, points)`

Tìm vị trí của một điểm dọc theo hướng đường thẳng:

```typescript
export function nthDirectionOnLine(vec: number[], p0: number[], points: number[][]): number {
    if (!points.includes(p0)) throw 'danh sách p không bao gồm p0';
    
    const _vec = vector(vec[0], vec[1]);
    const vecUnit = _vec.normalize();
    const vec0 = vector(p0[0], p0[1]);
    const allVec = new Set<Vector>();
    allVec.add(vec0);
    
    points.forEach(i => {
        if (i !== p0) allVec.add(vector(i[0], i[1]));
    });
    
    const _s = [...allVec].sort((vec1, vec2) => {
        const k1 = vec1.dot(vecUnit);
        const k2 = vec2.dot(vecUnit);
        return k1 < k2 ? -1 : k1 > k2 ? 1 : 0;
    });
    
    return _s.indexOf(vec0) + 1;
}
```

## Hàm Nâng Cao

### Sắp Xếp Theo Xoay

#### `sortByRotationV2(role, points, vec)`

Sắp xếp các điểm theo vị trí góc quanh tâm:

```typescript
export function sortByRotationV2(role: Point, points: Point[], vec?: Vector): Point[] {
    const _vec = vec || vector(1, 0); // Mặc định là trục x dương
    
    return points.sort((p1, p2) => {
        const angle1 = p1.equalTo(role) ? 0 : _vec.angleTo(vector(role, p1));
        const angle2 = p2.equalTo(role) ? 0 : _vec.angleTo(vector(role, p2));
        return angle1 < angle2 ? -1 : angle1 > angle2 ? 1 : 0;
    });
}
```

### So Sánh Hướng

#### `directionOfPointOnParallelVector(vec, root, p)`

Xác định hướng của một điểm tương đối với vector song song:

```typescript
export function directionOfPointOnParallelVector(
    vec: number[], 
    root: number[], 
    p: number[]
): 1 | -1 {
    const _vec = vector(vec[0], vec[1]);
    const vecUnit = _vec.normalize();
    const vecRoot = vector(root[0], root[1]);
    const vecP = vector(p[0], p[1]);
    
    const k1 = vecRoot.dot(vecUnit);
    const k2 = vecP.dot(vecUnit);
    
    return k1 < k2 ? -1 : 1;
}
```

### So Sánh Chỉ Số

#### `isLargerThan(sourceRelIdx, targetRelIdx)`

So sánh các chỉ số tương đối xem xét phần tử preview vs thực:

```typescript
export function isLargerThan(sourceRelIdx: number, targetRelIdx: number): boolean {
    return (
        (sourceRelIdx < 0 && targetRelIdx < 0 && sourceRelIdx < targetRelIdx) ||
        (sourceRelIdx >= 0 && targetRelIdx >= 0 && sourceRelIdx > targetRelIdx) ||
        (sourceRelIdx < 0 && targetRelIdx >= 0)
    );
}
```

## Ví Dụ Sử Dụng

### Xây Dựng Hình Vuông

```typescript
export class CreateSquareTool extends GeometryTool<CommonToolState> {
    private createSquare(vertices: SelectedVertex[]): void {
        const [v1, v2] = vertices;
        
        // Tính toán hướng cho xây dựng hình vuông
        const direction = nthDirectionByLine(
            [v2.x - v1.x, v2.y - v1.y], 
            [v1.x, v1.y], 
            [v2.x, v2.y]
        );
        
        // Sử dụng hướng để xác định định hướng hình vuông
        const sideVector = [v2.x - v1.x, v2.y - v1.y];
        const perpVector = direction === 1 ? 
            [-sideVector[1], sideVector[0]] : 
            [sideVector[1], -sideVector[0]];
        
        // Tính toán các đỉnh còn lại
        const v3 = [v2.x + perpVector[0], v2.y + perpVector[1]];
        const v4 = [v1.x + perpVector[0], v1.y + perpVector[1]];
        
        this.constructSquare([v1, v2, v3, v4]);
    }
}
```

### Sắp Xếp Điểm Giao

```typescript
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    private orderIntersectionPoints(
        intersections: Point[], 
        referenceLine: RenderLine
    ): Point[] {
        const lineVector = referenceLine.vector();
        const startPoint = referenceLine.startPoint();
        
        return intersections.map(point => ({
            point,
            position: nthDirectionOnLineV2(lineVector, point, [startPoint, point])
        }))
        .sort((a, b) => a.position - b.position)
        .map(item => item.point);
    }
}
```

### Sắp Xếp Đỉnh Góc

```typescript
export class CreateAngleByThreePointsTool extends GeometryTool<CommonToolState> {
    private orderAngleVertices(vertices: RenderVertex[]): RenderVertex[] {
        if (vertices.length !== 3) return vertices;
        
        const [center, p1, p2] = vertices;
        const centerPoint = point(center.x, center.y);
        const points = [point(p1.x, p1.y), point(p2.x, p2.y)];
        
        // Sắp xếp điểm theo xoay quanh tâm
        const sorted = sortByRotationV2(centerPoint, points);
        
        return [center, ...sorted.map(p => 
            vertices.find(v => v.x === p.x && v.y === p.y)
        )];
    }
}
```

## Tích Hợp Với Công Cụ

### Xây Dựng Nhận Biết Hướng

Các công cụ sử dụng thuật toán hướng để đảm bảo xây dựng nhất quán:

```typescript
// Trong xây dựng đa giác
const orderedVertices = sortByRotationV2(centerPoint, vertices);

// Trong xây dựng đường song song
const direction = directionOfPointOnParallelVector(
    lineVector, 
    linePoint, 
    targetPoint
);

// Trong xử lý giao điểm
const intersectionOrder = nthDirectionOnLine(
    lineVector, 
    firstIntersection, 
    allIntersections
);
```

### Định Vị Phần Tử Preview

Thuật toán hướng giúp định vị các phần tử preview:

```typescript
onPointerMove(event: GeoPointerEvent): void {
    const currentPos = calculatePosInLayer(event, this.focusDocCtrl);
    
    if (this.baseVertex && this.currentVertex) {
        const direction = nthDirectionByLine(
            [this.currentVertex.x - this.baseVertex.x, this.currentVertex.y - this.baseVertex.y],
            [this.baseVertex.x, this.baseVertex.y],
            [currentPos.x, currentPos.y]
        );
        
        // Cập nhật preview dựa trên hướng
        this.updatePreview(direction);
    }
}
```

## Nền Tảng Toán Học

### Phép Toán Vector

Module sử dụng toán học vector cho tính toán hướng:

```typescript
// Vector từ điểm A đến điểm B
const vector_AB = vector(pointA, pointB);

// Góc giữa các vector
const angle = vectorA.angleTo(vectorB);

// Tích vô hướng cho phép chiếu
const projection = vectorA.dot(vectorB.normalize());
```

### Đo Góc

Góc được đo bằng radian với các quy ước cụ thể:

- 0 radian: Hướng trục X dương
- π/2 radian: Hướng trục Y dương
- π radian: Hướng trục X âm
- 3π/2 radian: Hướng trục Y âm

### Biến Đổi Tọa Độ

Module xử lý các biến đổi hệ tọa độ:

```typescript
// Tọa độ hình học sang tọa độ lớp
const layerX = renderer.geoToLayerX(geoX);
const layerY = renderer.geoToLayerY(geoY);

// Tọa độ lớp sang tọa độ hình học
const geoX = renderer.layerToGeoX(layerX);
const geoY = renderer.layerToGeoY(layerY);
```

## Cân Nhắc Hiệu Suất

### Caching

Các tính toán hướng được cache khi có thể:

```typescript
private directionCache = new Map<string, number>();

private getCachedDirection(key: string, calculator: () => number): number {
    if (!this.directionCache.has(key)) {
        this.directionCache.set(key, calculator());
    }
    return this.directionCache.get(key);
}
```

### Tối Ưu Hóa

- Sử dụng vector chuẩn hóa cho tính toán nhất quán
- Cache các phép toán lượng giác đắt đỏ
- Giảm thiểu so sánh số thực
- Sử dụng kiểm tra bằng nhau dựa trên epsilon

## Thực Hành Tốt Nhất

1. **Thứ Tự Nhất Quán**: Luôn sử dụng cùng thuật toán hướng cho các phép toán tương tự
2. **Xử Lý Trường Hợp Biên**: Kiểm tra vector có độ dài bằng không và điểm thẳng hàng
3. **Sử Dụng Độ Chính Xác Phù Hợp**: Xem xét độ chính xác số thực trong so sánh
4. **Cache Kết Quả**: Cache các tính toán hướng đắt đỏ khi có thể
5. **Tài Liệu Quy Ước**: Tài liệu hóa rõ ràng các quy ước hệ tọa độ
