# Tài Liệu Hệ Thống Objects

## Tổng Quan

Hệ thống objects quản lý bộ điều khiển tài li<PERSON>, phát hiện va chạm và tương tác đối tượng hình học. <PERSON><PERSON> cung cấp cơ sở hạ tầng cốt lõi cho quản lý tài liệu, lựa chọn phần tử và xử lý tương tác người dùng.

## Kiến Trúc

### Các Thành Phần Objects

```
objects/
├── geo.document.ctrl.ts    # Bộ điều khiển và quản lý tài liệu
└── hit.checker.ts          # Phát hiện va chạm và lựa chọn
```

## Bộ Điều Khiển Tài Liệu

### Lớp GeoDocCtrl

Bộ điều khiển tài liệu ch<PERSON>h quản lý các tài liệ<PERSON> hình học:

```typescript
export class GeoDocCtrl extends DefaultVDocCtrl implements HasBoundaryCtrl {
    rendererCtrl: GeoRenderer;
    
    private readonly _docRenderProp$: BehaviorSubject<DocRenderProp>;
    private readonly _selectedElements$: BehaviorSubject<GeoRenderElement[]>;
    public readonly selectedElements$: Observable<GeoRenderElement[]>;
    
    constructor(
        public override editor: GeometryEditor,
        public override state: GeoDoc,
        public override viewport: BoardViewportManager
    ) {
        super(state, editor, viewport);
        
        this._docRenderProp$ = new BehaviorSubject<DocRenderProp>(state.docRenderProp);
        this._selectedElements$ = new BehaviorSubject<GeoRenderElement[]>([]);
        this.selectedElements$ = this._selectedElements$.asObservable();
    }
}
```

### Thuộc Tính Tài Liệu

```typescript
interface DocumentProperties {
    // Thuộc tính render tài liệu
    get docRenderProp$(): Observable<DocRenderProp>;
    get docRenderProp(): DocRenderProp;
    set docRenderProp(value: DocRenderProp);
    
    // Thuộc tính render phần tử mặc định
    get docDefaultElRenderProps(): DefaultGeoRenderProp;
    set docDefaultElRenderProps(value: DefaultGeoRenderProp);
    
    // Phần tử được chọn
    get selectedElements(): GeoRenderElement[];
    get selectedElements$(): Observable<GeoRenderElement[]>;
}
```

### Quản Lý Renderer

```typescript
// Gắn renderer vào tài liệu
attachRenderer(renderer: GeoRenderer): void {
    this.rendererCtrl = renderer;
    this.layers.push(renderer.layer);
    this.state.layers.push(renderer.layer.state as GeoLayer);
}

// Xóa renderer
onRemove(): void {
    const idx = this.layers.indexOf(this.rendererCtrl.layer);
    this.state.layers.splice(idx, 1);
    this.layers.splice(idx, 1);
    this.rendererCtrl.onRemoved();
    this.viewport.removeLayer(this.rendererCtrl.layer);
}
```

### Quản Lý Trạng Thái Tài Liệu

```typescript
// Xây dựng trạng thái render cho đồng bộ hóa
buildGeoRenderDocState(): GeoRenderDocState {
    return {
        docId: this.state.globalId,
        canvasWidth: this.rendererCtrl.width,
        canvasHeight: this.rendererCtrl.height,
        numDim: this.state.kind,
        ...this.state.docRenderProp,
    } as GeoRenderDocState;
}

// Cập nhật ranh giới tài liệu
updateBoundary(boundary: BoundaryRectangle): void {
    for (let i = 0; i < this.state.layers.length; i++) {
        const layer = this.layers[i];
        this.state.layers[i].boundary = boundary;
        
        if (this.isBoundedView()) {
            (layer as BoundedGraphicLayerCtrl).updateBoundary(boundary);
        }
    }
}
```

## Hệ Thống Phát Hiện Va Chạm

### Mô Hình Hit Context

```typescript
export interface GeoHitContextDetails extends SelectHitContextDetails {
    el: GeoRenderElement;
    hitMargin: number;
    epsilon?: number;
}

export interface GeoSelectHitContext extends SelectHitContext {
    doc: GeoDocCtrl;
    hitDetails: GeoHitContextDetails;
}
```

### Hàm Phát Hiện Va Chạm

#### Phát Hiện Va Chạm Ranh Giới Phần Tử

```typescript
export function checkHitOnElementBoundary2D(
    posInGeo: Position,
    el: GeoRenderElement,
    renderer: GeoRenderer,
    doc: GeoDocCtrl,
    zoomLevel: number,
    epsilon: number
): GeoSelectHitContext | undefined {
    const docRenderProp = doc.state.docRenderProp;
    const lineWeight = el.renderProp['lineWeight'] ?? 1;
    const e = EPSILON(lineWeight / 2, docRenderProp.screenUnit * docRenderProp.scale, zoomLevel) + epsilon;
    
    switch (el.type) {
        case 'RenderVertex':
            return checkHitOnVertex2D(posInGeo, el as RenderVertex, doc, e);
        case 'RenderLine':
        case 'RenderLineSegment':
        case 'RenderVector':
        case 'RenderRay':
            return checkHitOnLine2D(posInGeo, el as RenderLine, doc, renderer, e);
        case 'RenderCircle':
            return checkHitOnCircle2D(posInGeo, el as RenderCircle, doc, e);
        case 'RenderEllipse':
            return checkHitOnEllipse2D(posInGeo, el as RenderEllipse, doc, renderer, e);
        case 'RenderSector':
            return checkHitOnSector2D(posInGeo, el as RenderSector, doc, renderer, e);
        default:
            return undefined;
    }
}
```

#### Phát Hiện Va Chạm Hàng Loạt

```typescript
export function checkHitOnElementsBoundary2D(
    doc: GeoDocCtrl,
    els: GeoRenderElement[],
    posInGeo: Position,
    renderer: GeoRenderer,
    zoomLevel: number,
    epsilon?: number
): GeoSelectHitContext[] {
    return els
        .filter(el => doc.editor.filterElementFunc(el) && (el as StrokeType).length !== undefined)
        .sort((a, b) => ((a as StrokeType).length > (b as StrokeType).length ? 1 : -1))
        .map(el => checkHitOnElementBoundary2D(posInGeo, el, renderer, doc, zoomLevel, epsilon))
        .filter(h => h && true);
}

export function checkHitOnPoints(
    doc: GeoDocCtrl,
    els: GeoRenderElement[],
    posInGeo: Position,
    renderer: GeoRenderer,
    zoomLevel: number,
    epsilon?: number
): GeoSelectHitContext[] {
    return els
        .filter(el => doc.editor.filterElementFunc(el) && el instanceof RenderVertex)
        .map(el => checkHitOnElementBoundary2D(posInGeo, el, renderer, doc, zoomLevel, epsilon))
        .filter(h => h && true);
}
```

### Phát Hiện Va Chạm Cụ Thể

#### Phát Hiện Va Chạm Vertex

```typescript
function checkHitOnVertex2D(
    posInGeo: Position,
    vertex: RenderVertex,
    doc: GeoDocCtrl,
    epsilon: number
): GeoSelectHitContext | undefined {
    const distance = Math.sqrt(
        Math.pow(posInGeo.x - vertex.x, 2) + 
        Math.pow(posInGeo.y - vertex.y, 2)
    );
    
    if (distance <= epsilon) {
        return {
            doc: doc,
            hitDetails: {
                hitId: vertex.relIndex,
                el: vertex,
                hitMargin: distance,
                epsilon: epsilon,
            }
        };
    }
    
    return undefined;
}
```

#### Phát Hiện Va Chạm Line

```typescript
function checkHitOnLine2D(
    posInGeo: Position,
    line: RenderLine,
    doc: GeoDocCtrl,
    renderer: GeoRenderer,
    epsilon: number
): GeoSelectHitContext | undefined {
    // Tính khoảng cách từ điểm đến đường thẳng
    const distance = Math.abs(
        line.a * posInGeo.x + line.b * posInGeo.y + line.c
    ) / Math.sqrt(line.a * line.a + line.b * line.b);
    
    if (distance <= epsilon) {
        // Đối với đoạn thẳng, kiểm tra điểm có nằm trong ranh giới đoạn thẳng
        if (line.type === 'LineSegment') {
            const startPoint = line.startPoint(renderer);
            const endPoint = line.endPoint(renderer);
            
            if (!isPointOnSegment(posInGeo, startPoint, endPoint)) {
                return undefined;
            }
        }
        
        return {
            doc: doc,
            hitDetails: {
                hitId: line.relIndex,
                el: line,
                hitMargin: distance,
                epsilon: epsilon,
            }
        };
    }
    
    return undefined;
}
```

#### Phát Hiện Va Chạm Circle

```typescript
function checkHitOnCircle2D(
    posInGeo: Position,
    circle: RenderCircle,
    doc: GeoDocCtrl,
    epsilon: number
): GeoSelectHitContext | undefined {
    const centerDistance = Math.sqrt(
        Math.pow(posInGeo.x - circle.centerX, 2) + 
        Math.pow(posInGeo.y - circle.centerY, 2)
    );
    
    const radiusDistance = Math.abs(centerDistance - circle.radius);
    
    if (radiusDistance <= epsilon) {
        return {
            doc: doc,
            hitDetails: {
                hitId: circle.relIndex,
                el: circle,
                hitMargin: radiusDistance,
                epsilon: epsilon,
            }
        };
    }
    
    return undefined;
}
```

### Phát Hiện Va Chạm Bên Trong

#### Phát Hiện Bên Trong Polygon

```typescript
function checkHitInsidePolygon2D(
    posInGeo: Position,
    polygon: RenderPolygon,
    doc: GeoDocCtrl,
    renderer: GeoRenderer
): GeoSelectHitContext | undefined {
    const vertices = polygon.vertices(renderer);
    
    if (isPointInsidePolygon(posInGeo, vertices)) {
        return {
            doc: doc,
            hitDetails: {
                hitId: polygon.relIndex,
                el: polygon,
                hitMargin: 0,
            }
        };
    }
    
    return undefined;
}

function isPointInsidePolygon(point: Position, vertices: Point[]): boolean {
    let inside = false;
    const n = vertices.length;
    
    for (let i = 0, j = n - 1; i < n; j = i++) {
        const vi = vertices[i];
        const vj = vertices[j];
        
        if (((vi.y > point.y) !== (vj.y > point.y)) &&
            (point.x < (vj.x - vi.x) * (point.y - vi.y) / (vj.y - vi.y) + vi.x)) {
            inside = !inside;
        }
    }
    
    return inside;
}
```

#### Phát Hiện Bên Trong Circle

```typescript
function checkHitInsideCircle2D(
    posInGeo: Position,
    circle: RenderCircleShape,
    doc: GeoDocCtrl,
    renderer: GeoRenderer
): GeoSelectHitContext | undefined {
    const distance = Math.sqrt(
        Math.pow(posInGeo.x - circle.centerX, 2) + 
        Math.pow(posInGeo.y - circle.centerY, 2)
    );
    
    if (distance <= circle.radius) {
        return {
            doc: doc,
            hitDetails: {
                hitId: circle.relIndex,
                el: circle,
                hitMargin: circle.radius - distance,
            }
        };
    }
    
    return undefined;
}
```

## Phát Hiện Va Chạm Tài Liệu

### Phương Thức Phát Hiện Va Chạm Chính

```typescript
checkHit(
    event: LocatableEvent<any>,
    layer: BoundedGraphicLayerCtrl,
    useRelaxedHitPrecision = false,
    preview = false
): GeoSelectHitContext {
    const isTouchEvent = event.nativeEvent['pointerType'] == 'touch';
    const posInGeo = calculatePosInLayer(event, this);
    
    if (!validatePointerPos(posInGeo, this)) {
        return undefined;
    }
    
    const zoomLevel = calculateZoomLevel(this, this.viewport);
    const epsilon = useRelaxedHitPrecision ? 
        (isTouchEvent ? TOUCH_HIT_EPSILON : RELAXED_HIT_EPSILON) : 
        (isTouchEvent ? TOUCH_HIT_EPSILON : NORMAL_HIT_EPSILON);
    
    const elements = preview ? 
        this.rendererCtrl.previewObjects.allElements() :
        this.rendererCtrl.originObjects.allElements();
    
    // Kiểm tra điểm trước (ưu tiên cao nhất)
    const pointHits = checkHitOnPoints(
        this, elements, posInGeo, this.rendererCtrl, zoomLevel, epsilon
    );
    if (pointHits.length > 0) {
        return pointHits[0];
    }
    
    // Kiểm tra ranh giới phần tử
    const boundaryHits = checkHitOnElementsBoundary2D(
        this, elements, posInGeo, this.rendererCtrl, zoomLevel, epsilon
    );
    if (boundaryHits.length > 0) {
        return boundaryHits[0];
    }
    
    // Kiểm tra bên trong phần tử (ưu tiên thấp nhất)
    const insideHits = checkHitOnElementsInside2D(
        this, elements, posInGeo, this.rendererCtrl
    );
    if (insideHits.length > 0) {
        return insideHits[0];
    }
    
    return undefined;
}
```

## Ví Dụ Sử Dụng

### Tạo và Quản Lý Tài Liệu

```typescript
// Tạo bộ điều khiển tài liệu
const docCtrl = new GeoDocCtrl(editor, geoDoc, viewport);

// Gắn renderer
const renderer = new Geo2dRenderer(layerCtrl, docCtrl);
docCtrl.attachRenderer(renderer);

// Đăng ký thay đổi tài liệu
docCtrl.docRenderProp$.subscribe(renderProp => {
    console.log('Thuộc tính render tài liệu đã thay đổi:', renderProp);
});

docCtrl.selectedElements$.subscribe(elements => {
    console.log('Phần tử được chọn đã thay đổi:', elements);
});
```

### Phát Hiện Va Chạm Trong Công Cụ

```typescript
export class SelectionTool extends GeometryTool<CommonToolState> {
    onPointerDown(event: GeoPointerEvent): void {
        const hitContext = this.focusDocCtrl.checkHit(
            event, 
            this.focusDocCtrl.rendererCtrl.layer,
            false, // Sử dụng độ chính xác bình thường
            false  // Kiểm tra phần tử thực
        );
        
        if (hitContext) {
            this.handleElementHit(hitContext);
        } else {
            this.handleEmptySpaceClick(event);
        }
    }
    
    private handleElementHit(hitContext: GeoSelectHitContext): void {
        const element = hitContext.hitDetails.el;
        const distance = hitContext.hitDetails.hitMargin;
        
        console.log(`Va chạm phần tử ${element.relIndex} tại khoảng cách ${distance}`);
        
        // Cập nhật lựa chọn
        this.focusDocCtrl.selectedElements = [element];
    }
}
```

### Phát Hiện Va Chạm Tùy Chỉnh

```typescript
// Phát hiện va chạm tùy chỉnh cho loại phần tử cụ thể
function checkHitOnCustomElement(
    posInGeo: Position,
    element: CustomRenderElement,
    doc: GeoDocCtrl,
    epsilon: number
): GeoSelectHitContext | undefined {
    // Triển khai logic phát hiện va chạm tùy chỉnh
    const isHit = customHitTestLogic(posInGeo, element, epsilon);
    
    if (isHit) {
        return {
            doc: doc,
            hitDetails: {
                hitId: element.relIndex,
                el: element,
                hitMargin: calculateHitMargin(posInGeo, element),
                epsilon: epsilon,
            }
        };
    }
    
    return undefined;
}
```

### Phép Toán Hàng Loạt

```typescript
// Chọn nhiều phần tử trong một vùng
function selectElementsInRegion(
    doc: GeoDocCtrl,
    region: Rectangle
): GeoRenderElement[] {
    const elements = doc.rendererCtrl.originObjects.allElements();
    const selected: GeoRenderElement[] = [];
    
    for (const element of elements) {
        if (isElementInRegion(element, region)) {
            selected.push(element);
        }
    }
    
    doc.selectedElements = selected;
    return selected;
}

function isElementInRegion(element: GeoRenderElement, region: Rectangle): boolean {
    switch (element.type) {
        case 'RenderVertex':
            const vertex = element as RenderVertex;
            return region.contains(vertex.x, vertex.y);
        case 'RenderLine':
            // Kiểm tra đường thẳng có giao với vùng
            return lineIntersectsRectangle(element as RenderLine, region);
        default:
            return false;
    }
}
```

## Tối Ưu Hóa Hiệu Suất

### Tối Ưu Hóa Phát Hiện Va Chạm

```typescript
// Sử dụng chỉ mục không gian cho số lượng lớn phần tử
class SpatialIndex {
    private quadTree: QuadTree;
    
    constructor(boundary: Rectangle) {
        this.quadTree = new QuadTree(boundary);
    }
    
    insert(element: GeoRenderElement): void {
        const bounds = this.getElementBounds(element);
        this.quadTree.insert(element, bounds);
    }
    
    query(region: Rectangle): GeoRenderElement[] {
        return this.quadTree.query(region);
    }
}

// Phát hiện va chạm tối ưu hóa sử dụng chỉ mục không gian
function optimizedHitDetection(
    posInGeo: Position,
    doc: GeoDocCtrl,
    epsilon: number
): GeoSelectHitContext | undefined {
    const searchRegion = new Rectangle(
        posInGeo.x - epsilon, posInGeo.y - epsilon,
        2 * epsilon, 2 * epsilon
    );
    
    const candidates = doc.spatialIndex.query(searchRegion);
    
    for (const element of candidates) {
        const hit = checkHitOnElementBoundary2D(
            posInGeo, element, doc.rendererCtrl, doc, 1.0, epsilon
        );
        if (hit) return hit;
    }
    
    return undefined;
}
```

## Thực Hành Tốt Nhất

1. **Ưu Tiên Va Chạm**: Kiểm tra điểm trước đường, đường trước hình
2. **Giá Trị Epsilon**: Sử dụng giá trị epsilon phù hợp cho các loại đầu vào khác nhau
3. **Hiệu Suất**: Sử dụng chỉ mục không gian cho số lượng lớn phần tử
4. **Độ Chính Xác**: Xem xét mức zoom khi tính toán dung sai va chạm
5. **Xác Thực**: Luôn xác thực vị trí con trỏ trước khi kiểm tra va chạm
6. **Caching**: Cache kết quả phát hiện va chạm khi phù hợp
