# Tài Liệu Hệ Thống Model

## Tổng Quan

Hệ thống model định nghĩa các cấu trúc dữ liệu, mô hình đối tượng và định nghĩa kiểu được sử dụng trong toàn bộ trình soạn thảo hình học. Nó cung cấp một hệ thống kiểu toàn diện cho các đối tượng hình học, thu<PERSON><PERSON> t<PERSON>h render và giao tiếp API.

## Kiến Trúc

### Danh Mục Model

Hệ thống model được tổ chức thành nhiều danh mục:

```
model/
├── geo.models.ts           # Kiểu hình học cốt lõi và cấu hình
├── geo.object.models.ts    # Bộ sưu tập và quản lý đối tượng
├── render.elements.model.ts # Phần tử render và thuộc tính
├── gateway.models.ts       # Mô hình giao tiếp API
├── geo.tool.model.ts      # Mô hình trạng thái công cụ
├── name.pattern.model.ts  # Mẫu đặt tên và xác thực
└── util.preview.ts        # Tiện ích preview
```

## Mô Hình Hình Học Cốt Lõi

### Các Loại Đối Tượng Hình Học

```typescript
export type GeoObjectType =
    | 'Point' | 'Line' | 'LineSegment' | 'Ray' | 'Vector'
    | 'Circle' | 'Ellipse' | 'Semicircle' | 'CircularSector'
    | 'Triangle' | 'RightTriangle' | 'IsoscelesTriangle' | 'EquilateralTriangle'
    | 'Polygon' | 'RegularPolygon' | 'Quadrilateral'
    | 'Rectangle' | 'Square' | 'Parallelogram' | 'Rhombus' | 'Trapezoid'
    | 'Angle';

export const GeoObjectTypeValue = [
    'Angle', 'Point', 'Vector', 'Line', 'LineSegment', 'Ray',
    'Circle', 'Ellipse', 'Semicircle', 'CircularSector',
    'Triangle', 'RightTriangle', 'IsoscelesTriangle',
    'IsoscelesRightTriangle', 'EquilateralTriangle',
    'Polygon', 'RegularPolygon', 'Quadrilateral',
    'Rectangle', 'Square', 'Parallelogram', 'Rhombus', 'Trapezoid'
];
```

### Các Loại Công Cụ

```typescript
export type GeometryToolType =
    | 'CreatePointTool' | 'CreateLineTool' | 'CreateCircleTool'
    | 'CreateTriangleTool' | 'CreateSquareTool' | 'CreateAngleTool'
    | 'IntersectionPointTool' | 'MiddlePointTool' | 'PointOnObjectTool'
    | 'CreateParallelLineTool' | 'CreatePerpendicularLineTool'
    | 'MoveElementTool' | 'UpdatePropTool' | 'RenameElementTool'
    | 'GeoPanTool' | 'GeoZoomTool' | 'InputCommandTool';
```

### Mô Hình Tài Liệu

```typescript
export interface GeoDoc extends VDoc {
    kind: GeoKind;
    docRenderProp: DocRenderProp;
    docDefaultElRenderProps: DefaultGeoRenderProp;
    layers: GeoLayer[];
}

export interface GeoLayer extends VDocLayer {
    boundary: BoundaryRectangle;
}

export type GeoDocInitData = {
    numDim: number;
    kind: GeoKind;
    boundary?: BoundaryRectangle;
    docRenderProp: DocRenderProp;
};
```

## Mô Hình Phần Tử Render

### Phần Tử Render Cơ Sở

```typescript
export abstract class GeoRenderElement {
    abstract readonly type: GeoRelType;
    abstract readonly elType: GeoObjectType;
    abstract renderProp?: GeoRenderProp;
    
    vertexRelIdxes?: number[];
    lineRelIdxes?: number[];
    arcRelIdx?: number;
    
    unselectable?: boolean;
    relIndex: number;
    name: string = '';
    usable: boolean = true;
    deleted?: boolean;
    valid: boolean = true;
    pInfo?: GeoPreviewInfo = undefined;
}
```

### Phần Tử Vertex

```typescript
export class RenderVertex extends GeoRenderElement {
    readonly type: GeoRelType = 'Vertex';
    readonly elType: GeoObjectType = 'Point';
    
    x: number;
    y: number;
    renderProp?: VertexGeoRenderProp;
    
    constructor(x: number, y: number, relIndex: number) {
        super();
        this.x = x;
        this.y = y;
        this.relIndex = relIndex;
    }
}
```

### Phần Tử Line

```typescript
export class RenderLine extends GeoRenderElement {
    readonly type: GeoRelType = 'Line';
    readonly elType: GeoObjectType = 'LineVi';
    
    a: number; // Phương trình đường thẳng: ax + by + c = 0
    b: number;
    c: number;
    renderProp?: LineGeoRenderProp;
    
    // Phương thức hỗ trợ
    vector(renderer?: GeoRenderer): number[] {
        return [-this.b, this.a]; // Vector hướng
    }
    
    distanceToPoint(x: number, y: number): number {
        return Math.abs(this.a * x + this.b * y + this.c) / 
               Math.sqrt(this.a * this.a + this.b * this.b);
    }
}

export class RenderLineSegment extends RenderLine {
    readonly type: GeoRelType = 'LineSegment';
    readonly elType: GeoObjectType = 'LineSegment';
    
    length?: number;
    
    startPoint(renderer?: GeoRenderer): Point {
        // Tính toán điểm bắt đầu từ chỉ số vertex
    }
    
    endPoint(renderer?: GeoRenderer): Point {
        // Tính toán điểm kết thúc từ chỉ số vertex
    }
}
```

### Phần Tử Circle

```typescript
export class RenderCircle extends GeoRenderElement {
    readonly type: GeoRelType = 'Circle';
    readonly elType: GeoObjectType = 'Circle';
    
    centerX: number;
    centerY: number;
    radius: number;
    renderProp?: CircleGeoRenderProp;
    
    length?: number; // Chu vi
    
    containsPoint(x: number, y: number): boolean {
        const dx = x - this.centerX;
        const dy = y - this.centerY;
        return dx * dx + dy * dy <= this.radius * this.radius;
    }
}

export class RenderCircleShape extends RenderCircle {
    readonly type: GeoRelType = 'CircleShape';
    area?: number;
}
```

### Phần Tử Polygon

```typescript
export class RenderPolygon extends GeoRenderElement {
    readonly type: GeoRelType = 'Polygon';
    readonly elType: GeoObjectType = 'Polygon';
    
    renderProp?: PolygonGeoRenderProp;
    area?: number;
    
    vertices(renderer?: GeoRenderer): Point[] {
        // Lấy vertices từ chỉ số vertex
    }
    
    containsPoint(x: number, y: number, renderer?: GeoRenderer): boolean {
        // Kiểm tra điểm trong đa giác
    }
}
```

## Thuộc Tính Render

### Thuộc Tính Render Cơ Sở

```typescript
export interface GeoRenderProp {
    lineWeight?: number;
    strokeColor?: string;
    fillColor?: string;
    strokeStyle?: GeoStrokeStyle;
    visible?: boolean;
    highlighted?: boolean;
    selected?: boolean;
}

export interface DefaultGeoRenderProp {
    vertex: VertexGeoRenderProp;
    line: LineGeoRenderProp;
    circle: CircleGeoRenderProp;
    polygon: PolygonGeoRenderProp;
    angle: AngleGeoRenderProp;
}
```

### Thuộc Tính Render Cụ Thể

```typescript
export interface VertexGeoRenderProp extends GeoRenderProp {
    radius?: number;
    shape?: 'circle' | 'square' | 'diamond';
}

export interface LineGeoRenderProp extends GeoRenderProp {
    lineType?: LineType;
    arrowStart?: boolean;
    arrowEnd?: boolean;
}

export interface CircleGeoRenderProp extends GeoRenderProp {
    showCenter?: boolean;
    showRadius?: boolean;
}

export interface PolygonGeoRenderProp extends GeoRenderProp {
    showVertices?: boolean;
    showSides?: boolean;
}
```

## Bộ Sưu Tập Đối Tượng

### GeoObjCollection

Quản lý các bộ sưu tập phần tử render hình học:

```typescript
export class GeoObjCollection {
    protected readonly _elements: Map<number, GeoRenderElement> = new Map();
    private readonly cachedPotentialSelection: Map<number, GeoRenderElement> = new Map();
    private readonly cachedStrokesByType: Map<GeoRelType, Map<number, StrokeType>> = new Map();
    
    // Quản lý phần tử
    addRenderElement(element: GeoRenderElement): void;
    removeByIds(ids: number[]): void;
    elementAt<T extends GeoRenderElement>(relIndex: number): T | undefined;
    
    // Truy vấn
    vertices(highlighted?: boolean): RenderVertex[];
    lines(highlighted?: boolean): RenderLine[];
    circles(highlighted?: boolean): RenderCircle[];
    polygons(highlighted?: boolean): RenderPolygon[];
    
    // Hỗ trợ lựa chọn
    potentialSelectionElements(): GeoRenderElement[];
    strokesByType(type: GeoRelType): Map<number, StrokeType>;
    
    // Quản lý trạng thái
    highlight(elIndexArr: number[]): void;
    removeHighlight(elIndexArr: number[]): void;
    select(elIndexArr: number[]): void;
}
```

## Mô Hình Gateway

### Giao Tiếp API

```typescript
export interface FetchDocResponse {
    docId: DocumentId;
    numDim: number;
    docRenderProp: DocRenderProp;
    docDefaultElRenderProp: DefaultGeoRenderProp;
    render: GeoRenderElement[];
}

export interface ConstructionRequest {
    construction: GeoElConstructionRequest;
    renderProp?: GeoRenderProp;
}

export interface GeoElConstructionRequest {
    ctId: string;
    elType: GeoObjectType;
    cgName: string;
    paramSpecs?: ParamSpecs[];
    name?: string;
    ctIdx?: number;
}

export interface ApplyConstructionResponse {
    render: GeoRenderElement[];
    elIdxes: number[];
    ctIndexes: number[];
    renameEls: RenameElementModel[];
}
```

### Đặc Tả Tham Số

```typescript
export interface ParamSpecs {
    paramKind: ParamKind;
    value?: number;
    name?: string;
    expression?: string;
    relIdx?: number;
    relType?: RelType;
}

export enum ParamKind {
    'value',
    'name',
    'expression',
}
```

## Mô Hình Trạng Thái Công Cụ

### Trạng Thái Công Cụ Cơ Sở

```typescript
export class CommonToolState implements ToolState {}

export class TriangleToolState extends CommonToolState {
    drawMode: number = 0; // 0: bằng 3 điểm, 1: bằng đáy và chiều cao
}

export class QuadToolState extends CommonToolState {
    drawMode: number = 0; // Các chế độ khác nhau cho tứ giác
}

export class RegularPolygonToolState extends CommonToolState {
    sideCount: number = 6;
    drawMode: number = 0; // 0: tâm và đỉnh, 1: cạnh
}
```

### Trạng Thái Quản Lý Phần Tử

```typescript
export interface ElementItem {
    relIdx: number;
    name: string;
    elType: GeoObjectType;
    relType: GeoRelType;
    hidden: boolean;
    highlighted: boolean;
    selected: boolean;
    depIdxes?: number[];
}

export type ElementItemActionType =
    | 'highlight' | 'remove-highlight' | 'select' | 'select-multiple'
    | 'hide' | 'show' | 'delete' | 'setting' | 'deps';

export interface ElementItemAction {
    actionType: ElementItemActionType;
    relIdx: number;
}
```

## Hệ Thống Preview

### Thông Tin Preview

```typescript
export interface GeoPreviewInfo {
    refPEl?: GeoRenderElement[]; // Phần tử preview được tham chiếu
    refREl?: GeoRenderElement[]; // Phần tử thực được tham chiếu
    previewType?: 'construction' | 'highlight' | 'selection';
}

export class PreviewQueue {
    private queue: GeoRenderElement[] = [];
    
    add(...elements: GeoRenderElement[]): void;
    async flush(docCtrl: GeoDocCtrl): Promise<void>;
    clear(): void;
}
```

### Tiện Ích Preview

```typescript
// Tạo phần tử preview
export function pVertex(x: number, y: number, id: number): RenderVertex;
export function pLine(a: number, b: number, c: number, id: number): RenderLine;
export function pCircle(cx: number, cy: number, r: number, id: number): RenderCircle;
export function pAngle(vertex: Point, start: Point, end: Point, id: number): RenderAngle;

// Xây dựng thuộc tính preview
export function buildPreviewVertexRenderProp(): VertexGeoRenderProp;
export function buildPreviewLineRenderProp(): LineGeoRenderProp;
export function buildPreviewCircleRenderProp(): CircleGeoRenderProp;
```

## Mẫu Đặt Tên

### Xác Thực Tên

```typescript
export const RenderNamePatternModel = {
    RenderVertex: {
        relType: ['Vertex'],
        name: 'Điểm',
        regex: [/^[A-Z]\d*'?$/],
    },
    RenderLine: {
        relType: ['Line', 'LineSegment', 'Vector', 'Ray'],
        name: 'Đường thẳng',
        regex: [/^[a-z]\d*'?$/],
    },
    RenderLineSegment: {
        relType: ['Line', 'LineSegment', 'Vector', 'Ray'],
        name: 'Đoạn thẳng',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    RenderAngle: {
        relType: ['Angle'],
        name: 'Góc',
        regex: [/^([A-Z]\d*'?)$/, /^([A-Za-z]\d*'?)([A-Z]\d*'?)([A-Za-z]\d*'?)$/],
    }
};
```

## Ví Dụ Sử Dụng

### Tạo Phần Tử Render

```typescript
// Tạo một vertex
const vertex = new RenderVertex(10, 20, 1);
vertex.renderProp = {
    radius: 3,
    strokeColor: 'blue',
    fillColor: 'lightblue'
};

// Tạo một đường thẳng
const line = new RenderLine();
line.a = 1; line.b = -1; line.c = 0; // Phương trình đường thẳng: x - y = 0
line.relIndex = 2;
line.vertexRelIdxes = [1, 3]; // Tham chiếu đến vertices
```

### Làm Việc Với Bộ Sưu Tập

```typescript
const collection = new GeoObjCollection();

// Thêm phần tử
collection.addRenderElement(vertex);
collection.addRenderElement(line);

// Truy vấn phần tử
const vertices = collection.vertices();
const lines = collection.lines();
const highlighted = collection.vertices(true);

// Quản lý lựa chọn
collection.highlight([1, 2]);
collection.select([1]);
```

### Yêu Cầu Xây Dựng

```typescript
const pointConstruction: GeoElConstructionRequest = {
    ctId: 'Point',
    elType: 'Point',
    cgName: 'PointByCoordinates',
    paramSpecs: [
        { paramKind: ParamKind.value, value: 10 },
        { paramKind: ParamKind.value, value: 20 }
    ]
};

const lineConstruction: GeoElConstructionRequest = {
    ctId: 'Line',
    elType: 'LineVi',
    cgName: 'LineByTwoPoints',
    paramSpecs: [
        { paramKind: ParamKind.name, relIdx: 1, relType: 'Vertex' },
        { paramKind: ParamKind.name, relIdx: 2, relType: 'Vertex' }
    ]
};
```

## Thực Hành Tốt Nhất

1. **An Toàn Kiểu**: Sử dụng kiểu TypeScript một cách nhất quán
2. **Tính Bất Biến**: Ưu tiên các phép toán bất biến khi có thể
3. **Xác Thực**: Xác thực dữ liệu tại các ranh giới
4. **Hiệu Suất**: Sử dụng cấu trúc dữ liệu hiệu quả cho bộ sưu tập
5. **Nhất Quán**: Tuân theo quy ước đặt tên và mẫu
6. **Tài Liệu**: Tài liệu hóa các mối quan hệ model phức tạp
