# Nth Direction Documentation

## Overview

The nth.direction module provides algorithms for calculating geometric directions and positions. It handles spatial relationships between points, lines, and other geometric objects, ensuring consistent positioning and ordering in geometric constructions.

## Core Concepts

### Direction Calculation

Direction algorithms determine the relative position of points and objects in geometric space. This is crucial for:

- Consistent object placement
- Maintaining geometric relationships
- Ensuring predictable construction behavior
- Handling object transformations

### Coordinate Systems

The module works with 2D coordinate systems and handles:

- Point positions as `[x, y]` arrays
- Vector directions as `[dx, dy]` arrays
- Angular measurements in radians
- Relative positioning between objects

## Core Functions

### Line-Based Direction

#### `nthDirectionByLine(vec, role, p)`

Determines which side of a line a point lies on:

```typescript
export function nthDirectionByLine(vec: number[], role: number[], p: number[]): number {
    const _v = vector(vec[0], vec[1]);
    const _role = point(role[0], role[1]);
    const _p = point(p[0], p[1]);
    const vector_role_p = vector(_role, _p);
    
    if (_v.length === 0 || vector_role_p.length === 0) return 1;
    
    const angle = _v.angleTo(vector_role_p);
    return angle < Math.PI ? 1 : 2;
}
```

**Parameters:**
- `vec`: Direction vector of the line
- `role`: Reference point on the line
- `p`: Point to test

**Returns:** 1 if point is on first side, 2 if on second side

#### `nthSideOfVector(ps, p1Idx, p2Idx, p3Idx, p1RelIdx, p2RelIdx)`

Determines which side of a vector a point lies on, considering element indices:

```typescript
export function nthSideOfVector(
    ps: Point[],
    p1Idx: number,
    p2Idx: number,
    p3Idx: number,
    p1RelIdx: number,
    p2RelIdx: number
): number {
    // Determine which point is the first point based on relIdx
    if ((p1RelIdx >= 0 && p2RelIdx >= 0 && p1RelIdx > p2RelIdx) || 
        (p1RelIdx < 0 && p2RelIdx >= 0)) {
        const temp = p1Idx;
        p1Idx = p2Idx;
        p2Idx = temp;
    }
    
    return nthDirectionByLineV2(vector(ps[p1Idx], ps[p2Idx]), ps[p1Idx], ps[p3Idx]);
}
```

### Rotational Direction

#### `nthDirectionRotation(vec, role, p0, points)`

Finds the nth position of a point when rotating around a center:

```typescript
export function nthDirectionRotation(
    vec: number[], 
    role: number[], 
    p0: number[], 
    points: number[][]
): number {
    const _v = vector(vec[0], vec[1]);
    const _p = new Set<Point>();
    const _p0 = point(p0[0], p0[1]);
    const _role = point(role[0], role[1]);
    
    _p.add(_p0);
    points.forEach(i => {
        if (i !== p0) _p.add(point(i[0], i[1]));
    });
    
    const _s = [..._p].sort((p1, p2) => {
        const angle1 = p1.equalTo(_role) ? 0 : _v.angleTo(vector(_role, p1));
        const angle2 = p2.equalTo(_role) ? 0 : _v.angleTo(vector(_role, p2));
        return angle1 < angle2 ? -1 : angle1 > angle2 ? 1 : 0;
    });
    
    return _s.indexOf(_p0) + 1;
}
```

### Linear Direction

#### `nthDirectionOnLine(vec, p0, points)`

Finds the position of a point along a line direction:

```typescript
export function nthDirectionOnLine(vec: number[], p0: number[], points: number[][]): number {
    if (!points.includes(p0)) throw 'p list is not included p0';
    
    const _vec = vector(vec[0], vec[1]);
    const vecUnit = _vec.normalize();
    const vec0 = vector(p0[0], p0[1]);
    const allVec = new Set<Vector>();
    allVec.add(vec0);
    
    points.forEach(i => {
        if (i !== p0) allVec.add(vector(i[0], i[1]));
    });
    
    const _s = [...allVec].sort((vec1, vec2) => {
        const k1 = vec1.dot(vecUnit);
        const k2 = vec2.dot(vecUnit);
        return k1 < k2 ? -1 : k1 > k2 ? 1 : 0;
    });
    
    return _s.indexOf(vec0) + 1;
}
```

## Advanced Functions

### Sorting by Rotation

#### `sortByRotationV2(role, points, vec)`

Sorts points by their angular position around a center:

```typescript
export function sortByRotationV2(role: Point, points: Point[], vec?: Vector): Point[] {
    const _vec = vec || vector(1, 0); // Default to positive x-axis
    
    return points.sort((p1, p2) => {
        const angle1 = p1.equalTo(role) ? 0 : _vec.angleTo(vector(role, p1));
        const angle2 = p2.equalTo(role) ? 0 : _vec.angleTo(vector(role, p2));
        return angle1 < angle2 ? -1 : angle1 > angle2 ? 1 : 0;
    });
}
```

### Direction Comparison

#### `directionOfPointOnParallelVector(vec, root, p)`

Determines the direction of a point relative to a parallel vector:

```typescript
export function directionOfPointOnParallelVector(
    vec: number[], 
    root: number[], 
    p: number[]
): 1 | -1 {
    const _vec = vector(vec[0], vec[1]);
    const vecUnit = _vec.normalize();
    const vecRoot = vector(root[0], root[1]);
    const vecP = vector(p[0], p[1]);
    
    const k1 = vecRoot.dot(vecUnit);
    const k2 = vecP.dot(vecUnit);
    
    return k1 < k2 ? -1 : 1;
}
```

### Index Comparison

#### `isLargerThan(sourceRelIdx, targetRelIdx)`

Compares relative indices considering preview vs real elements:

```typescript
export function isLargerThan(sourceRelIdx: number, targetRelIdx: number): boolean {
    return (
        (sourceRelIdx < 0 && targetRelIdx < 0 && sourceRelIdx < targetRelIdx) ||
        (sourceRelIdx >= 0 && targetRelIdx >= 0 && sourceRelIdx > targetRelIdx) ||
        (sourceRelIdx < 0 && targetRelIdx >= 0)
    );
}
```

## Usage Examples

### Square Construction

```typescript
export class CreateSquareTool extends GeometryTool<CommonToolState> {
    private createSquare(vertices: SelectedVertex[]): void {
        const [v1, v2] = vertices;
        
        // Calculate direction for square construction
        const direction = nthDirectionByLine(
            [v2.x - v1.x, v2.y - v1.y], 
            [v1.x, v1.y], 
            [v2.x, v2.y]
        );
        
        // Use direction to determine square orientation
        const sideVector = [v2.x - v1.x, v2.y - v1.y];
        const perpVector = direction === 1 ? 
            [-sideVector[1], sideVector[0]] : 
            [sideVector[1], -sideVector[0]];
        
        // Calculate remaining vertices
        const v3 = [v2.x + perpVector[0], v2.y + perpVector[1]];
        const v4 = [v1.x + perpVector[0], v1.y + perpVector[1]];
        
        this.constructSquare([v1, v2, v3, v4]);
    }
}
```

### Intersection Point Ordering

```typescript
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    private orderIntersectionPoints(
        intersections: Point[], 
        referenceLine: RenderLine
    ): Point[] {
        const lineVector = referenceLine.vector();
        const startPoint = referenceLine.startPoint();
        
        return intersections.map(point => ({
            point,
            position: nthDirectionOnLineV2(lineVector, point, [startPoint, point])
        }))
        .sort((a, b) => a.position - b.position)
        .map(item => item.point);
    }
}
```

### Angle Vertex Ordering

```typescript
export class CreateAngleByThreePointsTool extends GeometryTool<CommonToolState> {
    private orderAngleVertices(vertices: RenderVertex[]): RenderVertex[] {
        if (vertices.length !== 3) return vertices;
        
        const [center, p1, p2] = vertices;
        const centerPoint = point(center.x, center.y);
        const points = [point(p1.x, p1.y), point(p2.x, p2.y)];
        
        // Sort points by rotation around center
        const sorted = sortByRotationV2(centerPoint, points);
        
        return [center, ...sorted.map(p => 
            vertices.find(v => v.x === p.x && v.y === p.y)
        )];
    }
}
```

## Integration with Tools

### Direction-Aware Construction

Tools use direction algorithms to ensure consistent construction:

```typescript
// In polygon construction
const orderedVertices = sortByRotationV2(centerPoint, vertices);

// In parallel line construction
const direction = directionOfPointOnParallelVector(
    lineVector, 
    linePoint, 
    targetPoint
);

// In intersection handling
const intersectionOrder = nthDirectionOnLine(
    lineVector, 
    firstIntersection, 
    allIntersections
);
```

### Preview Element Positioning

Direction algorithms help position preview elements:

```typescript
onPointerMove(event: GeoPointerEvent): void {
    const currentPos = calculatePosInLayer(event, this.focusDocCtrl);
    
    if (this.baseVertex && this.currentVertex) {
        const direction = nthDirectionByLine(
            [this.currentVertex.x - this.baseVertex.x, this.currentVertex.y - this.baseVertex.y],
            [this.baseVertex.x, this.baseVertex.y],
            [currentPos.x, currentPos.y]
        );
        
        // Update preview based on direction
        this.updatePreview(direction);
    }
}
```

## Mathematical Foundation

### Vector Operations

The module uses vector mathematics for direction calculations:

```typescript
// Vector from point A to point B
const vector_AB = vector(pointA, pointB);

// Angle between vectors
const angle = vectorA.angleTo(vectorB);

// Dot product for projection
const projection = vectorA.dot(vectorB.normalize());
```

### Angular Measurements

Angles are measured in radians with specific conventions:

- 0 radians: Positive X-axis direction
- π/2 radians: Positive Y-axis direction
- π radians: Negative X-axis direction
- 3π/2 radians: Negative Y-axis direction

### Coordinate Transformations

The module handles coordinate system transformations:

```typescript
// Geometric to layer coordinates
const layerX = renderer.geoToLayerX(geoX);
const layerY = renderer.geoToLayerY(geoY);

// Layer to geometric coordinates
const geoX = renderer.layerToGeoX(layerX);
const geoY = renderer.layerToGeoY(layerY);
```

## Performance Considerations

### Caching

Direction calculations are cached when possible:

```typescript
private directionCache = new Map<string, number>();

private getCachedDirection(key: string, calculator: () => number): number {
    if (!this.directionCache.has(key)) {
        this.directionCache.set(key, calculator());
    }
    return this.directionCache.get(key);
}
```

### Optimization

- Use normalized vectors for consistent calculations
- Cache expensive trigonometric operations
- Minimize floating-point comparisons
- Use epsilon-based equality checks

## Best Practices

1. **Consistent Ordering**: Always use the same direction algorithms for similar operations
2. **Handle Edge Cases**: Check for zero-length vectors and collinear points
3. **Use Appropriate Precision**: Consider floating-point precision in comparisons
4. **Cache Results**: Cache expensive direction calculations when possible
5. **Document Conventions**: Clearly document coordinate system conventions
